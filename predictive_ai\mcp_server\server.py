"""
FastMCP Server for AI Data Science Pipeline
"""
import asyncio
import sys
import os
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP
from loguru import logger

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings

# Initialize FastMCP server
mcp = FastMCP("AI Data Science Pipeline")

@mcp.tool()
async def suggest_datasets(user_query: str, problem_type: Optional[str] = None) -> Dict[str, Any]:
    """
    Suggest relevant datasets based on user query and problem type.

    Args:
        user_query: Natural language description of what the user wants to do
        problem_type: Optional problem type (regression, classification, time_series, clustering)

    Returns:
        Dictionary containing dataset suggestions, reasoning, and confidence score
    """
    try:
        logger.info(f"Suggesting datasets for query: {user_query}")

        # Import here to avoid circular imports
        from mcp_server.tools.suggest_datasets import suggest_datasets_task

        # Execute Celery task
        task_result = suggest_datasets_task.delay(user_query, problem_type)
        result = task_result.get(timeout=60)  # Wait up to 60 seconds

        return result
    except Exception as e:
        logger.error(f"Error in suggest_datasets: {e}")
        return {
            "suggestions": [],
            "reasoning": f"Error occurred: {str(e)}",
            "confidence_score": 0.0
        }

@mcp.tool()
async def list_available_datasets(
    source_type: Optional[str] = None,
    size_category: Optional[str] = None,
    dataset_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    List all available datasets with optional filtering.

    Args:
        source_type: Filter by source type (file_upload, database, url)
        size_category: Filter by size (small, medium, large)
        dataset_name: Get details for specific dataset

    Returns:
        Dictionary containing list of datasets and metadata
    """
    try:
        logger.info("Listing available datasets")

        from mcp_server.tools.list_available_datasets import list_available_datasets_task

        # Execute Celery task
        task_result = list_available_datasets_task.delay(source_type, size_category, dataset_name)
        result = task_result.get(timeout=30)

        return result
    except Exception as e:
        logger.error(f"Error in list_available_datasets: {e}")
        return {
            "datasets": [],
            "total_count": 0,
            "filter_applied": "error",
            "error": str(e)
        }

@mcp.tool()
async def clean_data(dataset_path: str, cleaning_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Clean and preprocess a dataset.

    Args:
        dataset_path: Path to the dataset file
        cleaning_options: Optional cleaning configuration

    Returns:
        Dictionary containing cleaning results and cleaned dataset path
    """
    try:
        logger.info(f"Cleaning dataset: {dataset_path}")

        from mcp_server.tools.clean_data import clean_data_task

        # Execute Celery task
        task_result = clean_data_task.delay(dataset_path, cleaning_options)
        result = task_result.get(timeout=300)  # Wait up to 5 minutes

        return result
    except Exception as e:
        logger.error(f"Error in clean_data: {e}")
        return {
            "error": str(e),
            "original_shape": None,
            "cleaned_shape": None,
            "cleaning_summary": f"Error occurred during cleaning: {str(e)}"
        }