"""
FastMCP Server for AI Data Science Pipeline
"""
import asyncio
import sys
import os
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP
from loguru import logger

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings

# Initialize FastMCP server
mcp = FastMCP("AI Data Science Pipeline")

@mcp.tool()
async def suggest_datasets(user_query: str, problem_type: Optional[str] = None) -> Dict[str, Any]:
    """
    Suggest relevant datasets based on user query and problem type.

    Args:
        user_query: Natural language description of what the user wants to do
        problem_type: Optional problem type (regression, classification, time_series, clustering)

    Returns:
        Dictionary containing dataset suggestions, reasoning, and confidence score
    """
    try:
        logger.info(f"Suggesting datasets for query: {user_query}")

        # Import here to avoid circular imports
        from mcp_server.tools.suggest_datasets import suggest_datasets_task

        # Execute Celery task
        task_result = suggest_datasets_task.delay(user_query, problem_type)
        result = task_result.get(timeout=60)  # Wait up to 60 seconds

        return result
    except Exception as e:
        logger.error(f"Error in suggest_datasets: {e}")
        return {
            "suggestions": [],
            "reasoning": f"Error occurred: {str(e)}",
            "confidence_score": 0.0
        }

@mcp.tool()
async def list_available_datasets(
    source_type: Optional[str] = None,
    size_category: Optional[str] = None,
    dataset_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    List all available datasets with optional filtering.

    Args:
        source_type: Filter by source type (file_upload, database, url)
        size_category: Filter by size (small, medium, large)
        dataset_name: Get details for specific dataset

    Returns:
        Dictionary containing list of datasets and metadata
    """
    try:
        logger.info("Listing available datasets")

        from mcp_server.tools.list_available_datasets import list_available_datasets_task

        # Execute Celery task
        task_result = list_available_datasets_task.delay(source_type, size_category, dataset_name)
        result = task_result.get(timeout=30)

        return result
    except Exception as e:
        logger.error(f"Error in list_available_datasets: {e}")
        return {
            "datasets": [],
            "total_count": 0,
            "filter_applied": "error",
            "error": str(e)
        }

@mcp.tool()
async def clean_data(dataset_path: str, cleaning_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Clean and preprocess a dataset.

    Args:
        dataset_path: Path to the dataset file
        cleaning_options: Optional cleaning configuration

    Returns:
        Dictionary containing cleaning results and cleaned dataset path
    """
    try:
        logger.info(f"Cleaning dataset: {dataset_path}")

        from mcp_server.tools.clean_data import clean_data_task

        # Execute Celery task
        task_result = clean_data_task.delay(dataset_path, cleaning_options)
        result = task_result.get(timeout=300)  # Wait up to 5 minutes

        return result
    except Exception as e:
        logger.error(f"Error in clean_data: {e}")
        return {
            "error": str(e),
            "original_shape": None,
            "cleaned_shape": None,
            "cleaning_summary": f"Error occurred during cleaning: {str(e)}"
        }

@mcp.tool()
async def detect_problem_type(dataset_path: str, target_column: Optional[str] = None) -> Dict[str, Any]:
    """
    Detect the problem type (classification, regression, etc.) from the dataset.

    Args:
        dataset_path: Path to the dataset file
        target_column: Optional target column name

    Returns:
        Dictionary containing detected problem type, confidence, and reasoning
    """
    try:
        logger.info(f"Detecting problem type for: {dataset_path}")

        from mcp_server.tools.detect_problem_type import detect_problem_type_task

        # Execute Celery task
        task_result = detect_problem_type_task.delay(dataset_path, target_column)
        result = task_result.get(timeout=120)  # Wait up to 2 minutes

        return result
    except Exception as e:
        logger.error(f"Error in detect_problem_type: {e}")
        return {
            "detected_problem_type": "classification",
            "confidence_score": 0.0,
            "reasoning": f"Error occurred: {str(e)}",
            "target_column": target_column or "unknown",
            "feature_columns": [],
            "alternative_problem_types": []
        }

@mcp.tool()
async def get_insights(dataset_path: str, target_column: Optional[str] = None) -> Dict[str, Any]:
    """
    Generate comprehensive data insights and statistics.

    Args:
        dataset_path: Path to the dataset file
        target_column: Optional target column for focused analysis

    Returns:
        Dictionary containing data insights, statistics, and visualizations
    """
    try:
        logger.info(f"Generating insights for: {dataset_path}")

        from mcp_server.tools.get_insights import get_insights_task

        # Execute Celery task
        task_result = get_insights_task.delay(dataset_path, target_column)
        result = task_result.get(timeout=180)  # Wait up to 3 minutes

        return result
    except Exception as e:
        logger.error(f"Error in get_insights: {e}")
        return {
            "basic_stats": {},
            "correlation_matrix": None,
            "missing_values": {},
            "data_types": {},
            "unique_values": {},
            "outliers": {},
            "visualizations": {},
            "insights_summary": f"Error occurred during analysis: {str(e)}"
        }

@mcp.tool()
async def train_model(
    dataset_path: str,
    problem_type: str,
    target_column: str,
    model_name: str = "random_forest",
    feature_columns: Optional[List[str]] = None,
    test_size: float = 0.2
) -> Dict[str, Any]:
    """
    Train a single machine learning model.

    Args:
        dataset_path: Path to the dataset file
        problem_type: Type of problem (classification, regression, etc.)
        target_column: Name of the target column
        model_name: Name of the model to train
        feature_columns: Optional list of feature columns to use
        test_size: Fraction of data to use for testing

    Returns:
        Dictionary containing training results and model information
    """
    try:
        logger.info(f"Training {model_name} model for {problem_type}")

        from mcp_server.tools.train_model import train_model_task

        # Execute Celery task
        task_result = train_model_task.delay(
            dataset_path, problem_type, target_column, model_name, feature_columns, test_size
        )
        result = task_result.get(timeout=600)  # Wait up to 10 minutes

        return result
    except Exception as e:
        logger.error(f"Error in train_model: {e}")
        return {
            "model_id": "error",
            "model_name": model_name,
            "model_path": None,
            "training_time": 0,
            "metrics": {},
            "feature_importance": None,
            "training_summary": f"Error occurred during training: {str(e)}"
        }

@mcp.tool()
async def train_multiple_models(
    dataset_path: str,
    problem_type: str,
    target_column: str,
    feature_columns: Optional[List[str]] = None,
    models_to_train: Optional[List[str]] = None,
    test_size: float = 0.2
) -> Dict[str, Any]:
    """
    Train multiple models and compare their performance.

    Args:
        dataset_path: Path to the dataset file
        problem_type: Type of problem (classification, regression, etc.)
        target_column: Name of the target column
        feature_columns: Optional list of feature columns to use
        models_to_train: Optional list of models to train
        test_size: Fraction of data to use for testing

    Returns:
        Dictionary containing results for all models and best model selection
    """
    try:
        logger.info(f"Training multiple models for {problem_type}")

        from mcp_server.tools.train_multiple_models import train_multiple_models_task

        # Execute Celery task
        task_result = train_multiple_models_task.delay(
            dataset_path, problem_type, target_column, feature_columns, models_to_train, test_size
        )
        result = task_result.get(timeout=1200)  # Wait up to 20 minutes

        return result
    except Exception as e:
        logger.error(f"Error in train_multiple_models: {e}")
        return {
            "results": [],
            "best_model": None,
            "comparison_summary": f"Error occurred during training: {str(e)}",
            "total_training_time": 0,
            "models_trained": 0
        }

@mcp.tool()
async def evaluate_model(
    model_path: str,
    test_dataset_path: str,
    target_column: str,
    feature_columns: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Evaluate a trained model on test data.

    Args:
        model_path: Path to the saved model file
        test_dataset_path: Path to the test dataset
        target_column: Name of the target column
        feature_columns: Optional list of feature columns to use

    Returns:
        Dictionary containing evaluation metrics and visualizations
    """
    try:
        logger.info(f"Evaluating model: {model_path}")

        from mcp_server.tools.evaluate_model import evaluate_model_task

        # Execute Celery task
        task_result = evaluate_model_task.delay(model_path, test_dataset_path, target_column, feature_columns)
        result = task_result.get(timeout=300)  # Wait up to 5 minutes

        return result
    except Exception as e:
        logger.error(f"Error in evaluate_model: {e}")
        return {
            "model_id": "error",
            "model_name": "unknown",
            "problem_type": "unknown",
            "metrics": {},
            "cross_validation_scores": {},
            "predictions": None,
            "evaluation_summary": f"Error occurred during evaluation: {str(e)}",
            "charts": {}
        }

@mcp.tool()
async def hyperparam_tune(
    dataset_path: str,
    problem_type: str,
    target_column: str,
    model_name: str = "random_forest",
    feature_columns: Optional[List[str]] = None,
    search_method: str = "grid",
    cv_folds: int = 5,
    n_iter: int = 50,
    test_size: float = 0.2
) -> Dict[str, Any]:
    """
    Perform hyperparameter tuning for a model.

    Args:
        dataset_path: Path to the dataset file
        problem_type: Type of problem (classification, regression, etc.)
        target_column: Name of the target column
        model_name: Name of the model to tune
        feature_columns: Optional list of feature columns to use
        search_method: Search method ("grid" or "random")
        cv_folds: Number of cross-validation folds
        n_iter: Number of iterations for random search
        test_size: Fraction of data to use for testing

    Returns:
        Dictionary containing tuning results and best parameters
    """
    try:
        logger.info(f"Tuning hyperparameters for {model_name}")

        from mcp_server.tools.hyperparam_tune import hyperparam_tune_task

        # Execute Celery task
        task_result = hyperparam_tune_task.delay(
            dataset_path, problem_type, target_column, model_name,
            feature_columns, search_method, cv_folds, n_iter, test_size
        )
        result = task_result.get(timeout=1800)  # Wait up to 30 minutes

        return result
    except Exception as e:
        logger.error(f"Error in hyperparam_tune: {e}")
        return {
            "model_id": "error",
            "model_name": model_name,
            "model_path": None,
            "best_parameters": {},
            "best_cv_score": 0.0,
            "test_score": 0.0,
            "tuning_time": 0,
            "search_method": search_method,
            "cv_results": {},
            "tuning_summary": f"Error occurred during tuning: {str(e)}"
        }

@mcp.tool()
async def fallback_recommender(
    context: str,
    error_message: str = "",
    step_name: str = "",
    user_feedback: Optional[str] = None
) -> Dict[str, Any]:
    """
    Generate fallback recommendations when primary methods fail.

    Args:
        context: Context information about the current situation
        error_message: Optional error message that occurred
        step_name: Name of the step that needs fallback options
        user_feedback: Optional user feedback for context

    Returns:
        Dictionary containing fallback recommendations and reasoning
    """
    try:
        logger.info(f"Generating fallback recommendations for: {step_name}")

        from mcp_server.tools.fallback_recommender import fallback_recommender_task

        # Execute Celery task
        task_result = fallback_recommender_task.delay(context, error_message, step_name, user_feedback)
        result = task_result.get(timeout=60)  # Wait up to 1 minute

        return result
    except Exception as e:
        logger.error(f"Error in fallback_recommender: {e}")
        return {
            "recommendation_type": "error",
            "step_name": step_name,
            "options": [],
            "reasoning": f"Error occurred while generating recommendations: {str(e)}",
            "confidence_score": 0.0,
            "error_context": error_message
        }

# Server startup
async def main():
    """Main server function"""
    logger.info("Starting AI Data Science Pipeline MCP Server")

    # Configure logging
    logger.add(
        settings.LOG_FILE,
        level=settings.LOG_LEVEL,
        rotation="1 day",
        retention="7 days"
    )

    logger.info(f"Server configured with settings: {settings.dict()}")

    # Start the server
    await mcp.run()

if __name__ == "__main__":
    asyncio.run(main())