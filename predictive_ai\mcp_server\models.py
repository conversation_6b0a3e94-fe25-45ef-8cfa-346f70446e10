"""
Data models and schemas for the AI Data Science Pipeline
"""
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ProblemType(str, Enum):
    """Supported problem types"""
    REGRESSION = "regression"
    CLASSIFICATION = "classification"
    TIME_SERIES = "time_series"
    CLUSTERING = "clustering"


class DataSourceType(str, Enum):
    """Data source types"""
    FILE_UPLOAD = "file_upload"
    DATABASE = "database"
    URL = "url"


class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DatasetInfo(BaseModel):
    """Dataset information model"""
    name: str
    description: str
    source_type: DataSourceType
    file_path: Optional[str] = None
    table_name: Optional[str] = None
    url: Optional[str] = None
    size_mb: Optional[float] = None
    num_rows: Optional[int] = None
    num_columns: Optional[int] = None
    column_names: Optional[List[str]] = None
    column_types: Optional[Dict[str, str]] = None
    created_at: datetime = Field(default_factory=datetime.now)


class DataCleaningResult(BaseModel):
    """Data cleaning operation result"""
    original_shape: tuple
    cleaned_shape: tuple
    dropped_columns: List[str]
    missing_values_handled: Dict[str, str]
    outliers_removed: int
    duplicates_removed: int
    cleaning_summary: str
    cleaned_data_path: str


class ProblemDetectionResult(BaseModel):
    """Problem type detection result"""
    detected_problem_type: ProblemType
    confidence_score: float
    reasoning: str
    target_column: Optional[str] = None
    feature_columns: List[str]
    alternative_problem_types: List[Dict[str, Any]]


class ModelTrainingRequest(BaseModel):
    """Model training request"""
    dataset_path: str
    problem_type: ProblemType
    target_column: str
    feature_columns: Optional[List[str]] = None
    model_name: str
    hyperparameters: Optional[Dict[str, Any]] = None
    test_size: float = 0.2
    random_state: int = 42


class ModelTrainingResult(BaseModel):
    """Model training result"""
    model_id: str
    model_name: str
    model_path: str
    training_time: float
    metrics: Dict[str, float]
    feature_importance: Optional[Dict[str, float]] = None
    confusion_matrix: Optional[List[List[int]]] = None
    training_summary: str


class ModelEvaluationResult(BaseModel):
    """Model evaluation result"""
    model_id: str
    metrics: Dict[str, float]
    predictions: Optional[List[Any]] = None
    evaluation_summary: str
    charts: Optional[Dict[str, str]] = None  # Chart file paths


class HyperparameterTuningResult(BaseModel):
    """Hyperparameter tuning result"""
    best_params: Dict[str, Any]
    best_score: float
    tuning_time: float
    n_trials: int
    optimization_history: List[Dict[str, Any]]


class ProblemDetectionResult(BaseModel):
    """Problem type detection result"""
    detected_problem_type: ProblemType
    confidence_score: float
    reasoning: str
    target_column: str
    feature_columns: List[str]
    alternative_problem_types: List[Dict[str, Any]]
    dataset_analysis: Optional[Dict[str, Any]] = None


class ModelEvaluationResult(BaseModel):
    """Model evaluation result"""
    model_id: str
    model_name: str
    problem_type: str
    metrics: Dict[str, float]
    cross_validation_scores: Optional[Dict[str, Any]] = None
    predictions: Optional[List[float]] = None
    evaluation_summary: str
    charts: Optional[Dict[str, str]] = None


class DataInsights(BaseModel):
    """Data insights and statistics"""
    basic_stats: Dict[str, Any]
    correlation_matrix: Optional[Dict[str, Dict[str, float]]] = None
    missing_values: Dict[str, int]
    data_types: Dict[str, str]
    unique_values: Dict[str, int]
    outliers: Dict[str, int]
    visualizations: Dict[str, str]  # Chart file paths
    insights_summary: str


class FallbackRecommendation(BaseModel):
    """Fallback recommendation"""
    recommendation_type: str
    options: List[Dict[str, Any]]
    reasoning: str
    confidence_score: float


class PipelineStep(BaseModel):
    """Pipeline step information"""
    step_name: str
    step_type: str
    status: TaskStatus
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class PipelineState(BaseModel):
    """Complete pipeline state"""
    pipeline_id: str
    user_request: str
    current_step: str
    steps: List[PipelineStep]
    context: Dict[str, Any]
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class ChatMessage(BaseModel):
    """Chat message model"""
    message_id: str
    user_message: str
    ai_response: str
    context: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)


class UserFeedback(BaseModel):
    """User feedback on recommendations"""
    step_name: str
    recommendation_id: str
    action: str  # "approve", "reject", "modify"
    feedback_data: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class DatabaseConnection(BaseModel):
    """Database connection configuration"""
    host: str
    port: int
    database: str
    username: str
    password: str
    schema: Optional[str] = None


class FileUpload(BaseModel):
    """File upload information"""
    filename: str
    file_path: str
    file_size: int
    file_type: str
    upload_timestamp: datetime = Field(default_factory=datetime.now)


class TaskResult(BaseModel):
    """Generic task result"""
    task_id: str
    task_name: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: datetime
    completed_at: Optional[datetime] = None
    execution_time: Optional[float] = None


# Request/Response Models for API endpoints

class DatasetSuggestionRequest(BaseModel):
    """Request for dataset suggestions"""
    user_query: str
    problem_type: Optional[ProblemType] = None
    preferred_size: Optional[str] = None  # "small", "medium", "large"


class DatasetSuggestionResponse(BaseModel):
    """Response with dataset suggestions"""
    suggestions: List[DatasetInfo]
    reasoning: str
    confidence_score: float


class DataCleaningRequest(BaseModel):
    """Request for data cleaning"""
    dataset_path: str
    cleaning_options: Optional[Dict[str, Any]] = None
    auto_clean: bool = True


class ProblemDetectionRequest(BaseModel):
    """Request for problem type detection"""
    dataset_path: str
    target_column: Optional[str] = None


class MultiModelTrainingRequest(BaseModel):
    """Request for training multiple models"""
    dataset_path: str
    problem_type: ProblemType
    target_column: str
    feature_columns: Optional[List[str]] = None
    models_to_train: Optional[List[str]] = None
    test_size: float = 0.2


class MultiModelTrainingResponse(BaseModel):
    """Response from multi-model training"""
    results: List[ModelTrainingResult]
    best_model: ModelTrainingResult
    comparison_summary: str


class ChatRequest(BaseModel):
    """Chat request"""
    message: str
    pipeline_id: str
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """Chat response"""
    response: str
    suggestions: Optional[List[str]] = None
    context: Dict[str, Any]