"""
Fallback recommendation tool for the AI Data Science Pipeline
"""
import os
import sys
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from loguru import logger

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import FallbackRecommendation, ProblemType
from mcp_server.celery import task


class FallbackRecommender:
    """Fallback recommendation service for when primary methods fail"""

    def __init__(self):
        self.problem_types = PROBLEM_TYPES

    def generate_fallback_recommendations(
        self,
        context: str,
        error_message: str = "",
        step_name: str = "",
        user_feedback: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate fallback recommendations based on context and error"""
        logger.info(f"Generating fallback recommendations for step: {step_name}")

        recommendations = []

        # Determine recommendation type based on step and context
        if "problem_type" in step_name.lower() or "detect" in step_name.lower():
            recommendations = self._get_problem_type_fallbacks(context, error_message)
        elif "model" in step_name.lower() and "train" in step_name.lower():
            recommendations = self._get_model_training_fallbacks(context, error_message)
        elif "data" in step_name.lower() or "insight" in step_name.lower():
            recommendations = self._get_data_analysis_fallbacks(context, error_message)
        elif "evaluate" in step_name.lower():
            recommendations = self._get_evaluation_fallbacks(context, error_message)
        else:
            recommendations = self._get_general_fallbacks(context, error_message)

        # Add user feedback-based recommendations if available
        if user_feedback:
            feedback_recommendations = self._get_feedback_based_recommendations(user_feedback, context)
            recommendations.extend(feedback_recommendations)

        # Generate reasoning
        reasoning = self._generate_fallback_reasoning(step_name, error_message, len(recommendations))

        result = {
            "recommendation_type": "fallback",
            "step_name": step_name,
            "options": recommendations,
            "reasoning": reasoning,
            "confidence_score": 0.7,  # Moderate confidence for fallbacks
            "error_context": error_message
        }

        logger.info(f"Generated {len(recommendations)} fallback recommendations")
        return result

    def _get_problem_type_fallbacks(self, context: str, error_message: str) -> List[Dict[str, Any]]:
        """Get fallback recommendations for problem type detection"""
        recommendations = []

        # Manual problem type selection
        recommendations.append({
            "option_type": "manual_selection",
            "title": "Manual Problem Type Selection",
            "description": "Let the user manually select the problem type",
            "action": "show_problem_type_options",
            "parameters": {
                "available_types": list(PROBLEM_TYPES.keys()),
                "descriptions": {k: v["description"] for k, v in PROBLEM_TYPES.items()}
            },
            "confidence": 0.9
        })

        # Default to classification
        recommendations.append({
            "option_type": "default_classification",
            "title": "Default to Classification",
            "description": "Assume this is a classification problem and proceed",
            "action": "set_problem_type",
            "parameters": {"problem_type": "classification"},
            "confidence": 0.6
        })

        # Data exploration approach
        recommendations.append({
            "option_type": "data_exploration",
            "title": "Detailed Data Exploration",
            "description": "Perform comprehensive data analysis to infer problem type",
            "action": "detailed_data_analysis",
            "parameters": {"analysis_depth": "comprehensive"},
            "confidence": 0.8
        })

        return recommendations

    def _get_model_training_fallbacks(self, context: str, error_message: str) -> List[Dict[str, Any]]:
        """Get fallback recommendations for model training issues"""
        recommendations = []

        # Simpler model
        recommendations.append({
            "option_type": "simpler_model",
            "title": "Use Simpler Model",
            "description": "Try a simpler, more robust model like Linear Regression or Logistic Regression",
            "action": "train_simple_model",
            "parameters": {"model_type": "simple"},
            "confidence": 0.8
        })

        # Reduced dataset
        recommendations.append({
            "option_type": "reduced_dataset",
            "title": "Use Subset of Data",
            "description": "Train on a smaller subset of the data to avoid memory/computation issues",
            "action": "train_with_subset",
            "parameters": {"subset_ratio": 0.5},
            "confidence": 0.7
        })

        # Different preprocessing
        recommendations.append({
            "option_type": "alternative_preprocessing",
            "title": "Alternative Data Preprocessing",
            "description": "Try different preprocessing techniques (scaling, encoding, etc.)",
            "action": "alternative_preprocessing",
            "parameters": {"preprocessing_strategy": "robust"},
            "confidence": 0.6
        })

        # Manual feature selection
        recommendations.append({
            "option_type": "manual_features",
            "title": "Manual Feature Selection",
            "description": "Let user manually select which features to use for training",
            "action": "manual_feature_selection",
            "parameters": {},
            "confidence": 0.8
        })

        return recommendations

    def _get_data_analysis_fallbacks(self, context: str, error_message: str) -> List[Dict[str, Any]]:
        """Get fallback recommendations for data analysis issues"""
        recommendations = []

        # Basic statistics only
        recommendations.append({
            "option_type": "basic_stats",
            "title": "Basic Statistics Only",
            "description": "Provide only basic statistical measures without complex analysis",
            "action": "basic_statistics",
            "parameters": {"analysis_level": "basic"},
            "confidence": 0.9
        })

        # Sample-based analysis
        recommendations.append({
            "option_type": "sample_analysis",
            "title": "Sample-Based Analysis",
            "description": "Analyze a random sample of the data instead of the full dataset",
            "action": "sample_analysis",
            "parameters": {"sample_size": 1000},
            "confidence": 0.7
        })

        # Column-by-column analysis
        recommendations.append({
            "option_type": "column_wise",
            "title": "Column-by-Column Analysis",
            "description": "Analyze each column individually to avoid memory issues",
            "action": "iterative_analysis",
            "parameters": {"method": "column_wise"},
            "confidence": 0.8
        })

        return recommendations

    def _get_evaluation_fallbacks(self, context: str, error_message: str) -> List[Dict[str, Any]]:
        """Get fallback recommendations for model evaluation issues"""
        recommendations = []

        # Simple metrics only
        recommendations.append({
            "option_type": "simple_metrics",
            "title": "Basic Metrics Only",
            "description": "Calculate only basic performance metrics",
            "action": "basic_evaluation",
            "parameters": {"metrics": ["accuracy", "mse"]},
            "confidence": 0.9
        })

        # Cross-validation alternative
        recommendations.append({
            "option_type": "holdout_validation",
            "title": "Simple Train-Test Split",
            "description": "Use simple train-test split instead of cross-validation",
            "action": "holdout_evaluation",
            "parameters": {"test_size": 0.2},
            "confidence": 0.8
        })

        # Manual evaluation
        recommendations.append({
            "option_type": "manual_evaluation",
            "title": "Manual Model Assessment",
            "description": "Let user manually assess model performance",
            "action": "manual_evaluation",
            "parameters": {},
            "confidence": 0.6
        })

        return recommendations

    def _get_general_fallbacks(self, context: str, error_message: str) -> List[Dict[str, Any]]:
        """Get general fallback recommendations"""
        recommendations = []

        # Skip step
        recommendations.append({
            "option_type": "skip_step",
            "title": "Skip This Step",
            "description": "Skip this step and continue with the pipeline",
            "action": "skip_step",
            "parameters": {},
            "confidence": 0.5
        })

        # Retry with different parameters
        recommendations.append({
            "option_type": "retry_modified",
            "title": "Retry with Modified Parameters",
            "description": "Retry the step with more conservative parameters",
            "action": "retry_conservative",
            "parameters": {"conservative_mode": True},
            "confidence": 0.7
        })

        # Manual intervention
        recommendations.append({
            "option_type": "manual_intervention",
            "title": "Manual Intervention Required",
            "description": "This step requires manual user input to proceed",
            "action": "request_manual_input",
            "parameters": {},
            "confidence": 0.8
        })

        return recommendations

    def _get_feedback_based_recommendations(self, user_feedback: str, context: str) -> List[Dict[str, Any]]:
        """Generate recommendations based on user feedback"""
        recommendations = []

        feedback_lower = user_feedback.lower()

        # Analyze feedback for keywords
        if "simpler" in feedback_lower or "simple" in feedback_lower:
            recommendations.append({
                "option_type": "user_requested_simple",
                "title": "Simplified Approach",
                "description": f"User requested: {user_feedback}",
                "action": "simplify_approach",
                "parameters": {"user_feedback": user_feedback},
                "confidence": 0.9
            })

        if "different" in feedback_lower or "alternative" in feedback_lower:
            recommendations.append({
                "option_type": "user_requested_alternative",
                "title": "Alternative Method",
                "description": f"User requested: {user_feedback}",
                "action": "try_alternative",
                "parameters": {"user_feedback": user_feedback},
                "confidence": 0.8
            })

        if "manual" in feedback_lower or "myself" in feedback_lower:
            recommendations.append({
                "option_type": "user_manual_control",
                "title": "Manual User Control",
                "description": f"User wants manual control: {user_feedback}",
                "action": "enable_manual_control",
                "parameters": {"user_feedback": user_feedback},
                "confidence": 0.9
            })

        return recommendations

    def _generate_fallback_reasoning(self, step_name: str, error_message: str, num_options: int) -> str:
        """Generate reasoning for fallback recommendations"""
        reasoning_parts = []

        if error_message:
            reasoning_parts.append(f"The step '{step_name}' encountered an error: {error_message[:100]}...")
        else:
            reasoning_parts.append(f"The step '{step_name}' needs alternative approaches")

        reasoning_parts.append(f"Generated {num_options} fallback options to continue the pipeline")
        reasoning_parts.append("These alternatives are designed to be more robust and handle edge cases")

        if "model" in step_name.lower():
            reasoning_parts.append("Consider using simpler models or reduced datasets for better stability")
        elif "data" in step_name.lower():
            reasoning_parts.append("Data analysis issues can often be resolved with sampling or simplified approaches")

        return ". ".join(reasoning_parts)


# Celery task
@task(name="fallback_recommender")
def fallback_recommender_task(
    context: str,
    error_message: str = "",
    step_name: str = "",
    user_feedback: Optional[str] = None
) -> Dict[str, Any]:
    """Celery task for fallback recommendations"""
    try:
        logger.info("Starting fallback recommendation task")

        recommender = FallbackRecommender()
        result = recommender.generate_fallback_recommendations(
            context=context,
            error_message=error_message,
            step_name=step_name,
            user_feedback=user_feedback
        )

        logger.info("Fallback recommendation task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in fallback recommendation task: {e}")
        raise


# Celery task
@task(name="fallback_recommender")
def fallback_recommender_task(*args, **kwargs) -> Dict[str, Any]:
    """Celery task for the service"""
    try:
        logger.info("Starting task")

        service = FallbackRecommender()
        result = service.generate_fallback_recommendations(*args, **kwargs)

        logger.info("Task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in task: {e}")
        raise
