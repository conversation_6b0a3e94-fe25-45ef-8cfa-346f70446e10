"""
Model evaluation tool for the AI Data Science Pipeline
"""
import os
import sys
import pandas as pd
import numpy as np
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, classification_report, roc_curve, precision_recall_curve,
    mean_squared_error, mean_absolute_error, r2_score
)
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings
from mcp_server.models import ModelEvaluationResult
from mcp_server.celery import task


class ModelEvaluator:
    """Advanced model evaluation service"""

    def __init__(self):
        self.model_save_dir = settings.MODEL_SAVE_DIR
        self.charts_dir = os.path.join(settings.UPLOAD_DIR, "charts")
        os.makedirs(self.charts_dir, exist_ok=True)

    def evaluate_model(
        self,
        model_path: str,
        test_dataset_path: str,
        target_column: str,
        feature_columns: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Evaluate a trained model on test data"""
        logger.info(f"Evaluating model: {model_path}")

        # Load model
        model_data = self._load_model(model_path)
        model = model_data['model']
        preprocessor = model_data['preprocessor']
        model_name = model_data.get('model_name', 'unknown')

        # Load test data
        df_test = self._load_dataset(test_dataset_path)
        X_test, y_test = self._prepare_test_data(df_test, target_column, feature_columns)

        # Preprocess test data
        X_test_processed = self._preprocess_test_data(X_test, preprocessor)

        # Make predictions
        y_pred = model.predict(X_test_processed)

        # Get prediction probabilities if available
        y_pred_proba = None
        if hasattr(model, 'predict_proba'):
            try:
                y_pred_proba = model.predict_proba(X_test_processed)
            except:
                pass

        # Determine problem type
        problem_type = self._determine_problem_type(y_test, y_pred)

        # Calculate metrics
        metrics = self._calculate_detailed_metrics(y_test, y_pred, y_pred_proba, problem_type)

        # Generate visualizations
        charts = self._generate_evaluation_charts(
            y_test, y_pred, y_pred_proba, problem_type, model_name
        )

        # Cross-validation if possible
        cv_scores = self._perform_cross_validation(model, X_test_processed, y_test, problem_type)

        # Generate evaluation summary
        evaluation_summary = self._generate_evaluation_summary(
            model_name, problem_type, metrics, cv_scores
        )

        result = {
            "model_id": model_data.get('model_id', 'unknown'),
            "model_name": model_name,
            "problem_type": problem_type,
            "metrics": metrics,
            "cross_validation_scores": cv_scores,
            "predictions": y_pred.tolist() if len(y_pred) <= 1000 else None,  # Limit size
            "evaluation_summary": evaluation_summary,
            "charts": charts
        }

        logger.info("Model evaluation completed successfully")
        return result

    def _load_model(self, model_path: str) -> Dict[str, Any]:
        """Load trained model and preprocessor"""
        try:
            model_data = joblib.load(model_path)
            logger.info(f"Loaded model: {model_data.get('model_name', 'unknown')}")
            return model_data
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

    def _load_dataset(self, dataset_path: str) -> pd.DataFrame:
        """Load test dataset"""
        try:
            if dataset_path.endswith('.csv'):
                df = pd.read_csv(dataset_path)
            elif dataset_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(dataset_path)
            elif dataset_path.endswith('.json'):
                df = pd.read_json(dataset_path)
            else:
                raise ValueError(f"Unsupported file format: {dataset_path}")

            logger.info(f"Loaded test dataset with shape: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise

    def _prepare_test_data(self, df: pd.DataFrame, target_column: str, feature_columns: Optional[List[str]]) -> tuple:
        """Prepare test features and target"""
        if target_column not in df.columns:
            raise ValueError(f"Target column '{target_column}' not found in test dataset")

        # Get target
        y = df[target_column]

        # Get features
        if feature_columns:
            missing_cols = [col for col in feature_columns if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Feature columns not found in test dataset: {missing_cols}")
            X = df[feature_columns]
        else:
            X = df.drop(columns=[target_column])

        return X, y

    def _preprocess_test_data(self, X_test: pd.DataFrame, preprocessor: Dict) -> np.ndarray:
        """Preprocess test data using saved preprocessor"""
        try:
            numeric_columns = X_test.select_dtypes(include=[np.number]).columns
            categorical_columns = X_test.select_dtypes(include=['object']).columns

            # Process numeric features
            if len(numeric_columns) > 0 and 'numeric_imputer' in preprocessor:
                X_test_numeric = preprocessor['numeric_imputer'].transform(X_test[numeric_columns])
                X_test_numeric = preprocessor['scaler'].transform(X_test_numeric)
            else:
                X_test_numeric = np.array([]).reshape(len(X_test), 0)

            # Process categorical features
            if len(categorical_columns) > 0 and 'categorical_imputer' in preprocessor:
                X_test_categorical = preprocessor['categorical_imputer'].transform(X_test[categorical_columns])
                X_test_categorical = preprocessor['encoder'].transform(X_test_categorical)
            else:
                X_test_categorical = np.array([]).reshape(len(X_test), 0)

            # Combine features
            X_test_processed = np.hstack([X_test_numeric, X_test_categorical])
            return X_test_processed

        except Exception as e:
            logger.error(f"Error preprocessing test data: {e}")
            raise

    def _determine_problem_type(self, y_true, y_pred) -> str:
        """Determine problem type from predictions"""
        # Check if predictions are continuous or discrete
        if len(np.unique(y_true)) <= 20 and len(np.unique(y_pred)) <= 20:
            return "classification"
        else:
            return "regression"

    def _calculate_detailed_metrics(
        self,
        y_true,
        y_pred,
        y_pred_proba: Optional[np.ndarray],
        problem_type: str
    ) -> Dict[str, float]:
        """Calculate comprehensive metrics"""
        metrics = {}

        if problem_type == "classification":
            metrics['accuracy'] = accuracy_score(y_true, y_pred)

            # Handle binary vs multiclass
            unique_classes = len(np.unique(y_true))
            average = 'binary' if unique_classes == 2 else 'weighted'

            metrics['precision'] = precision_score(y_true, y_pred, average=average, zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred, average=average, zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred, average=average, zero_division=0)

            # ROC AUC for binary classification
            if unique_classes == 2 and y_pred_proba is not None:
                try:
                    metrics['roc_auc'] = roc_auc_score(y_true, y_pred_proba[:, 1])
                except:
                    metrics['roc_auc'] = 0.0

            # Additional classification metrics
            metrics['num_classes'] = unique_classes

        elif problem_type == "regression":
            metrics['mse'] = mean_squared_error(y_true, y_pred)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2_score'] = r2_score(y_true, y_pred)

            # Additional regression metrics
            metrics['mean_absolute_percentage_error'] = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

        return metrics

    def _perform_cross_validation(self, model, X, y, problem_type: str) -> Dict[str, Any]:
        """Perform cross-validation"""
        try:
            if problem_type == "classification":
                cv_scores = cross_val_score(model, X, y, cv=5, scoring='accuracy')
                scoring_metric = 'accuracy'
            else:
                cv_scores = cross_val_score(model, X, y, cv=5, scoring='r2')
                scoring_metric = 'r2'

            return {
                'scores': cv_scores.tolist(),
                'mean_score': cv_scores.mean(),
                'std_score': cv_scores.std(),
                'scoring_metric': scoring_metric
            }
        except Exception as e:
            logger.warning(f"Cross-validation failed: {e}")
            return {}

    def _generate_evaluation_charts(
        self,
        y_true,
        y_pred,
        y_pred_proba: Optional[np.ndarray],
        problem_type: str,
        model_name: str
    ) -> Dict[str, str]:
        """Generate evaluation charts"""
        charts = {}

        try:
            if problem_type == "classification":
                # Confusion Matrix
                cm_path = self._plot_confusion_matrix(y_true, y_pred, model_name)
                if cm_path:
                    charts['confusion_matrix'] = cm_path

                # ROC Curve for binary classification
                if len(np.unique(y_true)) == 2 and y_pred_proba is not None:
                    roc_path = self._plot_roc_curve(y_true, y_pred_proba[:, 1], model_name)
                    if roc_path:
                        charts['roc_curve'] = roc_path

            elif problem_type == "regression":
                # Prediction vs Actual scatter plot
                scatter_path = self._plot_prediction_scatter(y_true, y_pred, model_name)
                if scatter_path:
                    charts['prediction_scatter'] = scatter_path

                # Residuals plot
                residuals_path = self._plot_residuals(y_true, y_pred, model_name)
                if residuals_path:
                    charts['residuals'] = residuals_path

        except Exception as e:
            logger.warning(f"Error generating charts: {e}")

        return charts

    def _plot_confusion_matrix(self, y_true, y_pred, model_name: str) -> Optional[str]:
        """Plot confusion matrix"""
        try:
            plt.figure(figsize=(8, 6))
            cm = confusion_matrix(y_true, y_pred)
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
            plt.title(f'Confusion Matrix - {model_name}')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')

            chart_path = os.path.join(self.charts_dir, f'confusion_matrix_{model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting confusion matrix: {e}")
            return None

    def _plot_roc_curve(self, y_true, y_pred_proba, model_name: str) -> Optional[str]:
        """Plot ROC curve"""
        try:
            plt.figure(figsize=(8, 6))
            fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
            auc_score = roc_auc_score(y_true, y_pred_proba)

            plt.plot(fpr, tpr, label=f'ROC Curve (AUC = {auc_score:.3f})')
            plt.plot([0, 1], [0, 1], 'k--', label='Random')
            plt.xlabel('False Positive Rate')
            plt.ylabel('True Positive Rate')
            plt.title(f'ROC Curve - {model_name}')
            plt.legend()
            plt.grid(True)

            chart_path = os.path.join(self.charts_dir, f'roc_curve_{model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting ROC curve: {e}")
            return None

    def _plot_prediction_scatter(self, y_true, y_pred, model_name: str) -> Optional[str]:
        """Plot prediction vs actual scatter plot"""
        try:
            plt.figure(figsize=(8, 6))
            plt.scatter(y_true, y_pred, alpha=0.6)

            # Perfect prediction line
            min_val = min(min(y_true), min(y_pred))
            max_val = max(max(y_true), max(y_pred))
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', label='Perfect Prediction')

            plt.xlabel('Actual Values')
            plt.ylabel('Predicted Values')
            plt.title(f'Prediction vs Actual - {model_name}')
            plt.legend()
            plt.grid(True)

            chart_path = os.path.join(self.charts_dir, f'prediction_scatter_{model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting prediction scatter: {e}")
            return None

    def _plot_residuals(self, y_true, y_pred, model_name: str) -> Optional[str]:
        """Plot residuals"""
        try:
            residuals = y_true - y_pred

            plt.figure(figsize=(8, 6))
            plt.scatter(y_pred, residuals, alpha=0.6)
            plt.axhline(y=0, color='r', linestyle='--')
            plt.xlabel('Predicted Values')
            plt.ylabel('Residuals')
            plt.title(f'Residuals Plot - {model_name}')
            plt.grid(True)

            chart_path = os.path.join(self.charts_dir, f'residuals_{model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting residuals: {e}")
            return None

    def _generate_evaluation_summary(
        self,
        model_name: str,
        problem_type: str,
        metrics: Dict[str, float],
        cv_scores: Dict[str, Any]
    ) -> str:
        """Generate evaluation summary"""
        summary_parts = []

        summary_parts.append(f"Evaluation results for {model_name} ({problem_type})")

        if problem_type == "classification":
            if 'accuracy' in metrics:
                summary_parts.append(f"Test accuracy: {metrics['accuracy']:.4f}")
            if 'f1_score' in metrics:
                summary_parts.append(f"F1 score: {metrics['f1_score']:.4f}")
            if 'roc_auc' in metrics and metrics['roc_auc'] > 0:
                summary_parts.append(f"ROC AUC: {metrics['roc_auc']:.4f}")

        elif problem_type == "regression":
            if 'r2_score' in metrics:
                summary_parts.append(f"R² score: {metrics['r2_score']:.4f}")
            if 'rmse' in metrics:
                summary_parts.append(f"RMSE: {metrics['rmse']:.4f}")
            if 'mae' in metrics:
                summary_parts.append(f"MAE: {metrics['mae']:.4f}")

        # Cross-validation results
        if cv_scores and 'mean_score' in cv_scores:
            metric_name = cv_scores.get('scoring_metric', 'score')
            summary_parts.append(f"Cross-validation {metric_name}: {cv_scores['mean_score']:.4f} (±{cv_scores['std_score']:.4f})")

        return ". ".join(summary_parts)


# Celery task
@task(name="evaluate_model")
def evaluate_model_task(
    model_path: str,
    test_dataset_path: str,
    target_column: str,
    feature_columns: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Celery task for model evaluation"""
    try:
        logger.info("Starting model evaluation task")

        evaluator = ModelEvaluator()
        result = evaluator.evaluate_model(
            model_path=model_path,
            test_dataset_path=test_dataset_path,
            target_column=target_column,
            feature_columns=feature_columns
        )

        logger.info("Model evaluation task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in model evaluation task: {e}")
        raise


# Celery task
@task(name="evaluate_model")
def evaluate_model_task(*args, **kwargs) -> Dict[str, Any]:
    """Celery task for the service"""
    try:
        logger.info("Starting task")

        service = ModelEvaluator()
        result = service.evaluate_model(*args, **kwargs)

        logger.info("Task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in task: {e}")
        raise
