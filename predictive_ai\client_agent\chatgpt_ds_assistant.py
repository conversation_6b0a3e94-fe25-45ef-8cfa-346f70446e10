"""
CHATGPT-LIKE DATA SCIENCE ASSISTANT
Advanced AI assistant with context awareness, memory, and real implementation capabilities
"""

import os
import json
import re
from typing import Dict, List, Any, Optional
from loguru import logger
from datetime import datetime

# Import our custom components
from client_agent.context_alternatives_generator import ContextAlternativesGenerator
from client_agent.implementation_engine import ImplementationEngine

# Try to import OpenAI and Groq
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False


class ChatGPTDataScienceAssistant:
    """ChatGPT-like Data Science Assistant with real implementation capabilities"""
    
    def __init__(self):
        self.alternatives_generator = ContextAlternativesGenerator()
        self.implementation_engine = ImplementationEngine()
        self.conversation_memory = {}  # Per pipeline conversation history
        self.llm_client = self._initialize_llm()
        
    def _initialize_llm(self):
        """Initialize LLM client"""
        try:
            if OPENAI_AVAILABLE and os.getenv('OPENAI_API_KEY'):
                return OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
            elif GROQ_AVAILABLE and os.getenv('GROQ_API_KEY'):
                return Groq(api_key=os.getenv('GROQ_API_KEY'))
        except Exception as e:
            logger.warning(f"LLM initialization failed: {e}")
        return None
    
    def generate_rejection_alternatives(self, step_name: str, step_data: Dict[str, Any], 
                                      context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Generate context-specific alternatives when user rejects a step"""
        
        logger.info(f"Generating alternatives for rejected step: {step_name}")
        
        try:
            alternatives = self.alternatives_generator.generate_alternatives(
                step_name, step_data, context or {}
            )
            
            logger.info(f"Generated {len(alternatives)} alternatives for {step_name}")
            return alternatives
            
        except Exception as e:
            logger.error(f"Failed to generate alternatives: {e}")
            return self._get_fallback_alternatives(step_name)
    
    def execute_alternative(self, alternative: Dict[str, Any], step_data: Dict[str, Any], 
                          pipeline_id: str) -> Dict[str, Any]:
        """Execute a selected alternative"""
        
        logger.info(f"Executing alternative: {alternative.get('title', 'Unknown')}")
        
        try:
            result = self.implementation_engine.execute_alternative(
                alternative, step_data, pipeline_id
            )
            
            if result['success']:
                logger.info(f"Alternative executed successfully: {alternative.get('id')}")
            else:
                logger.error(f"Alternative execution failed: {result.get('error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Alternative execution error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def chat(self, message: str, pipeline_id: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Main chat method - ChatGPT-like interface for data science"""
        
        logger.info(f"Processing chat message: {message[:50]}...")
        
        try:
            # Store conversation context
            if pipeline_id not in self.conversation_memory:
                self.conversation_memory[pipeline_id] = []
            
            # Analyze user intent
            intent_analysis = self._analyze_user_intent(message, context or {})
            
            # Check if user wants to implement something
            if intent_analysis['requires_implementation']:
                return self._handle_implementation_request(
                    message, intent_analysis, context or {}, pipeline_id
                )
            else:
                return self._handle_information_request(
                    message, intent_analysis, context or {}, pipeline_id
                )
                
        except Exception as e:
            logger.error(f"Chat processing failed: {e}")
            return {
                "response": f"❌ I encountered an error: {str(e)}\n\nPlease try rephrasing your request.",
                "suggestions": ["Try rephrasing your question", "Be more specific", "Ask for help"],
                "error": str(e)
            }
    
    def _analyze_user_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user intent using advanced NLP patterns"""
        
        message_lower = message.lower().strip()
        current_step = context.get('current_step', '')
        
        # Implementation intent patterns
        implementation_patterns = [
            # Direct implementation requests
            (r'(?:create|add|make).*column.*group.*by', 'feature_engineering', 0.95),
            (r'(?:apply|use).*(?:standard|minmax|robust).*scal', 'data_cleaning', 0.9),
            (r'(?:fill|impute).*missing.*(?:mean|median|mode)', 'data_cleaning', 0.9),
            (r'(?:remove|handle).*outlier', 'data_cleaning', 0.9),
            (r'(?:train|use).*(?:random.?forest|xgboost|svm)', 'model_training', 0.9),
            (r'set.*(\w+).*to.*([0-9.]+)', 'parameter_change', 0.85),
            (r'(?:yes|proceed|implement|do it|go ahead)', 'confirmation', 0.9),
        ]
        
        # Check implementation patterns
        for pattern, intent_type, confidence in implementation_patterns:
            if re.search(pattern, message_lower):
                return {
                    'intent_type': intent_type,
                    'confidence': confidence,
                    'requires_implementation': True,
                    'pattern_matched': pattern
                }
        
        # Information request patterns
        info_patterns = [
            (r'(?:what|how|why|explain)', 'information_request', 0.8),
            (r'(?:show|display|tell)', 'information_request', 0.8),
            (r'(?:help|assist)', 'help_request', 0.7),
        ]
        
        for pattern, intent_type, confidence in info_patterns:
            if re.search(pattern, message_lower):
                return {
                    'intent_type': intent_type,
                    'confidence': confidence,
                    'requires_implementation': False,
                    'pattern_matched': pattern
                }
        
        # Default: check if it's a general implementation request
        implementation_keywords = ['create', 'add', 'make', 'apply', 'use', 'set', 'change']
        if any(keyword in message_lower for keyword in implementation_keywords):
            return {
                'intent_type': 'general_implementation',
                'confidence': 0.6,
                'requires_implementation': True
            }
        
        return {
            'intent_type': 'information_request',
            'confidence': 0.5,
            'requires_implementation': False
        }
    
    def _handle_implementation_request(self, message: str, intent_analysis: Dict[str, Any], 
                                     context: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Handle requests that require implementation"""
        
        try:
            # Execute the user request
            result = self.implementation_engine.execute_user_request(
                message, context.get('step_data', {}), pipeline_id, context
            )
            
            # Store conversation
            self._store_conversation(pipeline_id, message, result.get('description', 'Implementation completed'))
            
            if result['success']:
                response_text = f"✅ **Implementation Completed!**\n\n"
                response_text += f"**What I did:** {result['description']}\n\n"
                response_text += f"**Result:** {result.get('result_summary', 'Operation completed successfully')}\n\n"
                response_text += "💬 **What would you like to do next?**"
                
                return {
                    "response": response_text,
                    "implementation_completed": True,
                    "implementation_details": result,
                    "suggestions": self._generate_next_step_suggestions(context),
                    "show_input_field": True
                }
            else:
                # Implementation failed, provide helpful response
                response_text = f"❌ **Implementation Failed:** {result.get('error', 'Unknown error')}\n\n"
                
                # Try to get LLM help for better explanation
                llm_help = self._get_llm_help_for_failed_implementation(message, result, context)
                if llm_help:
                    response_text += f"**Suggestion:** {llm_help}\n\n"
                
                response_text += "💡 **Try:**\n"
                response_text += "• Rephrase your request with more details\n"
                response_text += "• Check if the required columns exist\n"
                response_text += "• Ask for help with the specific operation"
                
                return {
                    "response": response_text,
                    "implementation_completed": False,
                    "suggestions": [
                        "Help me rephrase this request",
                        "Show me available columns",
                        "Explain what went wrong",
                        "Try a different approach"
                    ],
                    "show_input_field": True
                }
                
        except Exception as e:
            logger.error(f"Implementation request handling failed: {e}")
            return {
                "response": f"❌ I encountered an error while trying to implement your request: {str(e)}",
                "implementation_completed": False,
                "suggestions": ["Try rephrasing your request", "Ask for help", "Be more specific"],
                "show_input_field": True
            }
    
    def _handle_information_request(self, message: str, intent_analysis: Dict[str, Any], 
                                  context: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Handle information requests and questions"""
        
        try:
            # Get LLM response if available
            llm_response = self._get_llm_response(message, context)
            
            if llm_response:
                response_text = llm_response
            else:
                # Fallback to rule-based response
                response_text = self._generate_fallback_response(message, context)
            
            # Store conversation
            self._store_conversation(pipeline_id, message, response_text)
            
            # Check if response suggests an implementation
            if self._response_suggests_implementation(response_text):
                response_text += "\n\n💬 **Would you like me to implement this?**"
                suggestions = [
                    "Yes, implement this",
                    "Explain the steps first",
                    "Show me alternatives",
                    "Modify the approach"
                ]
            else:
                suggestions = self._generate_contextual_suggestions(context)
            
            return {
                "response": response_text,
                "implementation_completed": False,
                "suggestions": suggestions,
                "show_input_field": True,
                "llm_used": bool(llm_response)
            }
            
        except Exception as e:
            logger.error(f"Information request handling failed: {e}")
            return {
                "response": f"❌ I encountered an error: {str(e)}",
                "suggestions": ["Try rephrasing your question", "Ask for help"],
                "show_input_field": True
            }

    def _get_llm_response(self, message: str, context: Dict[str, Any]) -> Optional[str]:
        """Get response from LLM with context"""

        if not self.llm_client:
            return None

        try:
            # Build context-aware prompt
            system_prompt = self._build_ds_system_prompt(context)

            if hasattr(self.llm_client, 'chat'):
                # OpenAI client
                response = self.llm_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": message}
                    ],
                    max_tokens=500,
                    temperature=0.7
                )
                return response.choices[0].message.content
            else:
                # Groq client
                response = self.llm_client.chat.completions.create(
                    model="llama3-8b-8192",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": message}
                    ],
                    max_tokens=500,
                    temperature=0.7
                )
                return response.choices[0].message.content

        except Exception as e:
            logger.error(f"LLM request failed: {e}")
            return None

    def _build_ds_system_prompt(self, context: Dict[str, Any]) -> str:
        """Build data science expert system prompt"""

        current_step = context.get('current_step', 'unknown')
        step_data = context.get('step_data', {})

        prompt = f"""You are an expert Data Science Assistant, like ChatGPT but specialized for data science tasks.

CURRENT CONTEXT:
- Pipeline Step: {current_step}
- Available Data: {step_data.get('dataset_path', 'Unknown')}
- Problem Type: {context.get('problem_type', 'Unknown')}

CAPABILITIES:
- Data cleaning and preprocessing
- Feature engineering and selection
- Model training and evaluation
- Hyperparameter tuning
- Statistical analysis and visualization

INSTRUCTIONS:
1. Provide clear, actionable advice
2. Suggest specific implementations when appropriate
3. Explain the reasoning behind recommendations
4. Be concise but comprehensive
5. If suggesting an implementation, end with "Shall I implement this for you?"

EXPERTISE AREAS:
- Pandas, NumPy, Scikit-learn
- Data visualization with Matplotlib/Seaborn
- Machine learning algorithms
- Statistical methods
- Best practices in data science

Respond as a helpful, knowledgeable data science expert."""

        return prompt

    def _generate_fallback_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generate fallback response when LLM is not available"""

        current_step = context.get('current_step', 'unknown')
        message_lower = message.lower()

        if 'column' in message_lower and 'group' in message_lower:
            return """🔧 **Feature Engineering Suggestion:**

To create a column by grouping data, I can help you with:

1. **Group by a categorical column** (e.g., zipcode, category)
2. **Calculate aggregation** (mean, sum, count, max, min)
3. **Create new feature** with the aggregated values

**Example:** "Create column by grouping zipcode and calculating average price"

This will create a new column where each row gets the average price for its zipcode group.

Shall I implement this for you?"""

        elif 'scaling' in message_lower or 'normalization' in message_lower:
            return """📊 **Data Scaling Options:**

I can apply different scaling methods:

1. **Standard Scaling:** Mean=0, Std=1 (good for normal distributions)
2. **Min-Max Scaling:** Scale to [0,1] range (good for bounded features)
3. **Robust Scaling:** Uses median and IQR (robust to outliers)

**Recommendation:** Standard scaling is most common, but if you have outliers, use Robust scaling.

Which scaling method would you like me to apply?"""

        elif 'missing' in message_lower or 'null' in message_lower:
            return """🔧 **Missing Value Handling:**

I can handle missing values using:

1. **Mean/Median Imputation:** Fill with average values
2. **Forward Fill:** Use previous value
3. **Drop Missing:** Remove rows with missing values
4. **Advanced Imputation:** KNN or iterative imputation

**Recommendation:** For numeric data, median imputation is often robust.

Which method would you like me to use?"""

        elif 'outlier' in message_lower:
            return """📈 **Outlier Detection & Handling:**

I can handle outliers using:

1. **IQR Method:** Remove values beyond 1.5*IQR from quartiles
2. **Z-Score Method:** Remove values with |z-score| > 3
3. **Isolation Forest:** Advanced outlier detection
4. **Capping:** Cap values at percentiles (e.g., 5th and 95th)

**Recommendation:** IQR method is robust and interpretable.

How would you like me to handle outliers?"""

        else:
            return f"""🤖 **Data Science Assistant - {current_step.replace('_', ' ').title()}**

I'm here to help with your data science pipeline! I can:

• **Data Cleaning:** Handle missing values, outliers, scaling
• **Feature Engineering:** Create new features, transformations
• **Model Training:** Try different algorithms and parameters
• **Analysis:** Explain results and provide insights

**Try asking:**
• "Create a column by grouping zipcode and calculating average price"
• "Apply standard scaling to numeric features"
• "Remove outliers using IQR method"
• "Train a Random Forest model"

What would you like me to help you with?"""

    def _get_llm_help_for_failed_implementation(self, message: str, result: Dict[str, Any],
                                              context: Dict[str, Any]) -> Optional[str]:
        """Get LLM help for failed implementation"""

        if not self.llm_client:
            return None

        try:
            error_prompt = f"""The user requested: "{message}"

The implementation failed with error: {result.get('error', 'Unknown error')}

As a data science expert, provide a helpful suggestion for how to fix this or try a different approach. Be specific and actionable."""

            if hasattr(self.llm_client, 'chat'):
                response = self.llm_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": error_prompt}],
                    max_tokens=200,
                    temperature=0.7
                )
                return response.choices[0].message.content

        except Exception as e:
            logger.error(f"LLM help request failed: {e}")

        return None

    def _response_suggests_implementation(self, response: str) -> bool:
        """Check if response suggests an implementation"""

        suggestion_indicators = [
            'shall i', 'would you like me to', 'should i', 'can i',
            'implement', 'apply', 'create', 'shall i implement'
        ]

        response_lower = response.lower()
        return any(indicator in response_lower for indicator in suggestion_indicators)

    def _generate_next_step_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Generate suggestions for next steps after implementation"""

        current_step = context.get('current_step', '')

        if 'data' in current_step.lower():
            return [
                "Create another feature",
                "Apply different scaling",
                "Handle missing values",
                "Remove outliers"
            ]
        elif 'model' in current_step.lower():
            return [
                "Try different algorithm",
                "Tune hyperparameters",
                "Evaluate performance",
                "Compare models"
            ]
        else:
            return [
                "Continue with pipeline",
                "Make another change",
                "Explain what was done",
                "Show next options"
            ]

    def _generate_contextual_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Generate contextual suggestions based on current step"""

        current_step = context.get('current_step', '')

        if 'clean' in current_step.lower() or 'preprocess' in current_step.lower():
            return [
                "Create a new feature column",
                "Apply scaling to numeric features",
                "Handle missing values",
                "Remove outliers from data"
            ]
        elif 'feature' in current_step.lower():
            return [
                "Create polynomial features",
                "Apply log transformation",
                "Select important features",
                "Create interaction features"
            ]
        elif 'model' in current_step.lower():
            return [
                "Try Random Forest algorithm",
                "Use XGBoost for better performance",
                "Tune hyperparameters",
                "Compare multiple models"
            ]
        else:
            return [
                "What can I do here?",
                "Explain the current step",
                "Show available options",
                "Help me understand"
            ]

    def _store_conversation(self, pipeline_id: str, user_message: str, ai_response: str):
        """Store conversation in memory"""

        if pipeline_id not in self.conversation_memory:
            self.conversation_memory[pipeline_id] = []

        self.conversation_memory[pipeline_id].append({
            'user': user_message,
            'ai': ai_response,
            'timestamp': datetime.now().isoformat()
        })

        # Keep only last 10 exchanges
        if len(self.conversation_memory[pipeline_id]) > 10:
            self.conversation_memory[pipeline_id] = self.conversation_memory[pipeline_id][-10:]

    def _get_fallback_alternatives(self, step_name: str) -> List[Dict[str, Any]]:
        """Get fallback alternatives when generation fails"""

        return [
            {
                "id": "retry_step",
                "title": "Retry with Different Approach",
                "description": f"Try {step_name} with modified parameters or different method",
                "action_type": "retry",
                "implementation": {
                    "type": "generic",
                    "operation": "retry_step",
                    "config": {"step_name": step_name}
                }
            },
            {
                "id": "custom_approach",
                "title": "Custom Implementation",
                "description": "Use the AI chat to specify exactly what you want",
                "action_type": "custom",
                "implementation": {
                    "type": "generic",
                    "operation": "custom",
                    "config": {"requires_chat": True}
                }
            }
        ]


# Global instance
chatgpt_ds_assistant = ChatGPTDataScienceAssistant()
