"""
Setup script for AI Data Science Pipeline
"""
import os
import subprocess
import sys
from pathlib import Path


def check_python_version():
    """Check if Python version is 3.8+"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def check_redis():
    """Check if Redis is available"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis is running")
        return True
    except Exception:
        print("❌ Redis is not running or not installed")
        print("   Please install and start Redis:")
        print("   - Windows: Download from https://redis.io/download")
        print("   - macOS: brew install redis && brew services start redis")
        print("   - Linux: sudo apt-get install redis-server")
        return False


def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False


def create_directories():
    """Create necessary directories"""
    directories = ["uploads", "models", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def setup_environment():
    """Setup environment file"""
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    print("📝 Setting up environment file...")
    
    # Get API keys from user
    openai_key = input("Enter your OpenAI API key (optional, press Enter to skip): ").strip()
    groq_key = input("Enter your Groq API key (optional, press Enter to skip): ").strip()
    
    # Create .env content
    env_content = f"""# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/predictive_ai

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_CONCURRENCY=3

# LLM Configuration
OPENAI_API_KEY={openai_key}
GROQ_API_KEY={groq_key}
DEFAULT_LLM_MODEL=gpt-3.5-turbo
FALLBACK_LLM_MODEL=mixtral-8x7b-32768

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS=csv,xlsx,xls,txt,json

# Model Training Configuration
MODEL_SAVE_DIR=./models
MAX_TRAINING_TIME=3600

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print("✅ Environment file created")
    return True


def main():
    """Main setup function"""
    print("🚀 Setting up AI Data Science Pipeline...")
    print("=" * 50)
    
    # Check requirements
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Check Redis after installing dependencies
    if not check_redis():
        print("⚠️  Redis is required but not running. Please start Redis and run setup again.")
        return False
    
    # Create directories
    create_directories()
    
    # Setup environment
    setup_environment()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Start Redis if not already running")
    print("2. Run: python main.py")
    print("3. Open your browser to http://localhost:8001")
    print("\n💡 For development mode:")
    print("- Celery worker: python main.py --component celery")
    print("- API server: python main.py --component api") 
    print("- Streamlit UI: python main.py --component ui")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
