import os

def create_project_structure():
    """
    Creates the specified folder and file structure for the 'predictive_ai' project.
    """
    base_dir = "predictive_ai"

    # Define the directories to be created
    directories = [
        os.path.join(base_dir, "mcp_server", "tools"),
        os.path.join(base_dir, "client_agent"),
        os.path.join(base_dir, "ui")
    ]

    # Create all necessary directories
    print(f"Creating directories under '{base_dir}/'...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  Created directory: {directory}")

    # Define the files to be created with their relative paths from base_dir
    files = [
        # mcp_server files
        os.path.join("mcp_server", "tools", "suggest_datasets.py"),
        os.path.join("mcp_server", "tools", "list_available_datasets.py"),
        os.path.join("mcp_server", "tools", "clean_data.py"),
        os.path.join("mcp_server", "tools", "detect_problem_type.py"),
        os.path.join("mcp_server", "tools", "train_model.py"),
        os.path.join("mcp_server", "tools", "train_multiple_models.py"),
        os.path.join("mcp_server", "tools", "evaluate_model.py"),
        os.path.join("mcp_server", "tools", "hyperparam_tune.py"),
        os.path.join("mcp_server", "tools", "get_insights.py"),
        os.path.join("mcp_server", "tools", "fallback_recommender.py"),
        os.path.join("mcp_server", "__init__.py"),
        os.path.join("mcp_server", "models.py"),
        os.path.join("mcp_server", "server.py"),
        os.path.join("mcp_server", "celery.py"),

        # client_agent files
        os.path.join("client_agent", "orchestrator.py"),
        os.path.join("client_agent", "llm_chat_agent.py"),
        os.path.join("client_agent", "__init__.py"),

        # ui files
        os.path.join("ui", "streamlit_ui.py"),
        os.path.join("ui", "__init__.py"),

        # Root project files
        "config.py",
        "main.py",
        "requirements.txt",
        ".env",
        "README.md"
    ]

    # Create all specified files
    print(f"\nCreating files under '{base_dir}/'...")
    for file_path in files:
        full_path = os.path.join(base_dir, file_path)
        # Create parent directories for the file if they don't exist (e.g., for .env, README.md)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'w') as f:
            # You can add initial content here if needed, e.g., f.write("# Initial content")
            pass # Creates an empty file
        print(f"  Created file: {full_path}")

    print("\nProject structure created successfully!")

if __name__ == "__main__":
    create_project_structure()
