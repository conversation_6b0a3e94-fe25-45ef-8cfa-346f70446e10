# 🤖 AI Data Science Pipeline

An interactive AI-powered data science pipeline that guides users step-by-step through the entire machine learning workflow, from dataset discovery to model deployment.

## 🌟 Features

- **Interactive Workflow**: Step-by-step guidance through the data science pipeline
- **AI-Powered Recommendations**: Intelligent suggestions at each step
- **Multi-Model Training**: Automatic training and comparison of multiple models
- **Real-time Chat Support**: Context-aware AI assistant for questions and explanations
- **Flexible Data Sources**: Support for file uploads and database connections
- **Advanced Data Cleaning**: Automated preprocessing with customizable options
- **Problem Type Detection**: Automatic detection of regression, classification, time series, and clustering problems
- **Hyperparameter Tuning**: Automated optimization of model parameters
- **Interactive Visualizations**: Rich charts and insights throughout the process

## 🏗️ Architecture

### MCP Server (`/mcp_server`)
- **FastMCP-based backend** providing tools for each pipeline step
- **Celery task queue** for async processing with concurrency control
- **Comprehensive tools** for dataset suggestion, cleaning, training, evaluation

### Client Orchestrator (`/client_agent`)
- **FastAPI-based orchestration** managing workflow and tool coordination
- **LangChain/LangGraph integration** for intelligent decision making
- **Fallback policies** for handling user rejections and modifications

### Streamlit UI (`/ui`)
- **Interactive web interface** with chat support and progress tracking
- **Real-time updates** and approval/rejection workflows
- **Rich visualizations** and data insights

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+**
2. **Redis** (for Celery task queue)
3. **PostgreSQL** (optional, for database datasets)

### Installation

1. **Clone and navigate to the project:**
```bash
cd predictive_ai
```

2. **Create and activate virtual environment:**
```bash
python -m venv venv
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Set up environment variables:**
```bash
# Copy the example .env file and edit with your API keys
cp .env .env.local
# Edit .env with your actual API keys:
# - OPENAI_API_KEY=your_openai_key
# - GROQ_API_KEY=your_groq_key
```

5. **Start Redis** (required for Celery):
```bash
# On Windows (if you have Redis installed):
redis-server
# On macOS:
brew services start redis
# On Linux:
sudo systemctl start redis
```

### Running the Application

**Option 1: Start all services at once**
```bash
python main.py
```

**Option 2: Start services individually (for development)**
```bash
# Terminal 1 - Celery worker
python main.py --component celery

# Terminal 2 - FastAPI orchestrator
python main.py --component api

# Terminal 3 - Streamlit UI
python main.py --component ui
```

### Access the Application

- **Streamlit UI**: http://localhost:8001
- **API Documentation**: http://localhost:8000/docs
- **API Orchestrator**: http://localhost:8000

## 📊 Usage Workflow

### 1. Start a Pipeline
- Open the Streamlit UI
- Describe your data science goal (e.g., "Predict house prices")
- The system will start the pipeline and suggest relevant datasets

### 2. Dataset Discovery
- Review AI-suggested datasets based on your goal
- Upload your own CSV/Excel files
- Connect to PostgreSQL databases
- Approve or reject suggestions

### 3. Data Cleaning
- Automatic data preprocessing with intelligent defaults
- Handle missing values, outliers, and feature engineering
- Review cleaning summary and approve changes

### 4. Problem Detection
- AI automatically detects problem type (regression, classification, etc.)
- Suggests target variables and feature columns
- Modify recommendations if needed

### 5. Model Training
- Train multiple models automatically based on problem type
- Compare performance across different algorithms
- View detailed metrics and model explanations

### 6. Model Evaluation
- Comprehensive evaluation with relevant metrics
- Visualizations and performance insights
- Model comparison and selection

### 7. Hyperparameter Tuning (Optional)
- Automated optimization of best-performing models
- Bayesian optimization for efficient parameter search
- Final model selection and saving

## 🛠️ Configuration

### Environment Variables (.env)

```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/predictive_ai

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
DEFAULT_LLM_MODEL=gpt-3.5-turbo
FALLBACK_LLM_MODEL=mixtral-8x7b-32768

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_EXTENSIONS=csv,xlsx,xls,txt,json

# Model Training Configuration
MODEL_SAVE_DIR=./models
MAX_TRAINING_TIME=3600  # 1 hour

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
```

### Supported Problem Types

- **Regression**: Predicting continuous numerical values
- **Classification**: Predicting categorical labels
- **Time Series**: Forecasting future values based on temporal data
- **Clustering**: Grouping similar data points

### Supported Models

**Regression & Classification:**
- Linear/Logistic Regression
- Random Forest
- XGBoost
- LightGBM
- CatBoost
- SVM (Classification only)

**Time Series:**
- ARIMA
- Prophet
- LSTM
- XGBoost (time series variant)

**Clustering:**
- K-Means
- DBSCAN
- Hierarchical Clustering
- Gaussian Mixture Models

## 🔧 Development

### Project Structure
```
predictive_ai/
├── mcp_server/           # MCP backend tools
│   ├── tools/           # Individual pipeline tools
│   ├── models.py        # Data models and schemas
│   ├── server.py        # FastMCP server
│   └── celery.py        # Celery configuration
├── client_agent/        # Orchestration layer
│   ├── orchestrator.py  # FastAPI workflow manager
│   └── llm_chat_agent.py # AI chat assistant
├── ui/                  # Streamlit interface
│   └── streamlit_ui.py  # Web UI
├── config.py            # Configuration settings
├── main.py              # Application entry point
├── requirements.txt     # Dependencies
└── .env                 # Environment variables
```

### Adding New Tools

1. Create a new tool in `mcp_server/tools/`
2. Add Celery task decorator
3. Register in `mcp_server/server.py`
4. Update orchestrator workflow if needed

### Extending LLM Support

The system supports multiple LLM providers with automatic fallback:
1. **OpenAI GPT** (primary)
2. **Groq** (fallback)
3. **Rule-based responses** (final fallback)

## 🐛 Troubleshooting

### Common Issues

**Redis Connection Error:**
```bash
# Ensure Redis is running
redis-cli ping
# Should return "PONG"
```

**Import Errors:**
```bash
# Ensure all dependencies are installed
pip install -r requirements.txt
```

**Port Already in Use:**
```bash
# Change ports in .env file
API_PORT=8001  # Instead of 8000
```

**LLM API Errors:**
- Verify API keys in `.env` file
- Check API rate limits and quotas
- System will fallback to rule-based responses if LLMs fail

### Logs

- Application logs: `./logs/app.log`
- Celery logs: Console output when running worker
- Streamlit logs: Console output when running UI

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For questions and support:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the API documentation at `/docs`

---

**Happy Data Science! 🚀📊**