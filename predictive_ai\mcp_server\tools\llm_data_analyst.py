"""
LLM-Powered Data Analysis and Feature Engineering
Leverages the power of language models for intelligent data cleaning and feature engineering
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from openai import OpenAI
import groq
from config import settings
import logging

logger = logging.getLogger(__name__)


class LLMDataAnalyst:
    """LLM-powered data analyst for intelligent data cleaning and feature engineering"""
    
    def __init__(self):
        self.openai_client = None
        self.groq_client = None

        # Initialize LLM clients with proper error handling
        try:
            if settings.OPENAI_API_KEY:
                self.openai_client = OpenAI(api_key=settings.OPENAI_API_KEY)
                logger.info("OpenAI client initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize OpenAI client: {e}")

        try:
            if settings.GROQ_API_KEY:
                self.groq_client = groq.Groq(api_key=settings.GROQ_API_KEY)
                logger.info("Groq client initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Groq client: {e}")

    def is_llm_available(self) -> bool:
        """Check if any LLM client is available"""
        return self.openai_client is not None or self.groq_client is not None

    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        models = []
        if self.openai_client:
            models.append("OpenAI GPT")
        if self.groq_client:
            models.append("Groq Mixtral")
        if not models:
            models.append("Rule-based fallback")
        return models
    
    def analyze_dataset(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Comprehensive LLM-powered dataset analysis"""
        
        # Generate dataset summary
        dataset_summary = self._generate_dataset_summary(df)
        
        # Get LLM analysis
        analysis_prompt = self._create_analysis_prompt(dataset_summary, user_query, problem_type)
        llm_analysis = self._call_llm(analysis_prompt)
        
        # Parse LLM response
        try:
            analysis_result = json.loads(llm_analysis)
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            analysis_result = self._parse_text_response(llm_analysis)
        
        return {
            "dataset_summary": dataset_summary,
            "llm_analysis": analysis_result,
            "recommendations": analysis_result.get("recommendations", []),
            "data_quality_issues": analysis_result.get("data_quality_issues", []),
            "feature_engineering_suggestions": analysis_result.get("feature_engineering_suggestions", [])
        }
    
    def generate_cleaning_strategy(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Generate intelligent data cleaning strategy using LLM"""
        
        # Analyze data quality issues
        quality_issues = self._identify_quality_issues(df)
        
        # Create cleaning strategy prompt
        strategy_prompt = self._create_cleaning_strategy_prompt(quality_issues, user_query, problem_type)
        llm_strategy = self._call_llm(strategy_prompt)
        
        try:
            strategy_result = json.loads(llm_strategy)
        except json.JSONDecodeError:
            strategy_result = self._parse_cleaning_strategy(llm_strategy)
        
        return strategy_result
    
    def suggest_feature_engineering(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Generate intelligent feature engineering suggestions using LLM"""
        
        # Analyze existing features
        feature_analysis = self._analyze_features(df)
        
        # Create feature engineering prompt
        fe_prompt = self._create_feature_engineering_prompt(feature_analysis, user_query, problem_type)
        llm_suggestions = self._call_llm(fe_prompt)
        
        try:
            fe_result = json.loads(llm_suggestions)
        except json.JSONDecodeError:
            fe_result = self._parse_feature_engineering(llm_suggestions)
        
        return fe_result
    
    def explain_cleaning_decisions(self, cleaning_actions: List[Dict], user_query: str) -> str:
        """Generate explanations for cleaning decisions using LLM"""
        
        explanation_prompt = f"""
        As a data science expert, explain why these data cleaning actions were taken for the user's goal: "{user_query}"
        
        Cleaning actions performed:
        {json.dumps(cleaning_actions, indent=2)}
        
        Provide a clear, concise explanation of:
        1. Why each action was necessary
        2. How it helps achieve the user's goal
        3. What would happen if we didn't take these actions
        4. Any trade-offs or considerations
        
        Keep the explanation accessible to non-technical users while being informative.
        """
        
        return self._call_llm(explanation_prompt)
    
    def _generate_dataset_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive dataset summary"""
        
        summary = {
            "shape": df.shape,
            "columns": list(df.columns),
            "dtypes": df.dtypes.to_dict(),
            "missing_values": df.isnull().sum().to_dict(),
            "missing_percentages": (df.isnull().sum() / len(df) * 100).to_dict(),
            "numeric_columns": list(df.select_dtypes(include=[np.number]).columns),
            "categorical_columns": list(df.select_dtypes(include=['object', 'category']).columns),
            "unique_counts": df.nunique().to_dict(),
            "memory_usage": df.memory_usage(deep=True).sum(),
        }
        
        # Add basic statistics for numeric columns
        if summary["numeric_columns"]:
            summary["numeric_stats"] = df[summary["numeric_columns"]].describe().to_dict()
        
        # Add sample data
        summary["sample_data"] = df.head(3).to_dict('records')
        
        return summary
    
    def _identify_quality_issues(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Identify data quality issues"""
        
        issues = {
            "missing_values": {},
            "duplicates": df.duplicated().sum(),
            "outliers": {},
            "data_types": {},
            "inconsistencies": {}
        }
        
        # Missing values analysis
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                issues["missing_values"][col] = {
                    "count": int(missing_count),
                    "percentage": round((missing_count / len(df)) * 100, 2)
                }
        
        # Outlier detection for numeric columns
        for col in df.select_dtypes(include=[np.number]).columns:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            outliers = df[(df[col] < Q1 - 1.5 * IQR) | (df[col] > Q3 + 1.5 * IQR)]
            if len(outliers) > 0:
                issues["outliers"][col] = {
                    "count": len(outliers),
                    "percentage": round((len(outliers) / len(df)) * 100, 2)
                }
        
        # Data type inconsistencies
        for col in df.columns:
            if df[col].dtype == 'object':
                # Check if numeric data is stored as string
                try:
                    pd.to_numeric(df[col].dropna())
                    issues["data_types"][col] = "numeric_stored_as_string"
                except:
                    pass
        
        return issues
    
    def _analyze_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze existing features for engineering opportunities"""
        
        analysis = {
            "numeric_features": {},
            "categorical_features": {},
            "datetime_features": {},
            "text_features": {},
            "relationships": {}
        }
        
        # Analyze numeric features
        for col in df.select_dtypes(include=[np.number]).columns:
            analysis["numeric_features"][col] = {
                "min": float(df[col].min()),
                "max": float(df[col].max()),
                "mean": float(df[col].mean()),
                "std": float(df[col].std()),
                "skewness": float(df[col].skew()),
                "unique_count": int(df[col].nunique())
            }
        
        # Analyze categorical features
        for col in df.select_dtypes(include=['object', 'category']).columns:
            analysis["categorical_features"][col] = {
                "unique_count": int(df[col].nunique()),
                "most_frequent": str(df[col].mode().iloc[0]) if not df[col].mode().empty else None,
                "sample_values": df[col].dropna().unique()[:5].tolist()
            }
        
        # Check for potential datetime columns
        for col in df.select_dtypes(include=['object']).columns:
            sample_values = df[col].dropna().head(10)
            if any(self._looks_like_date(str(val)) for val in sample_values):
                analysis["datetime_features"][col] = {
                    "sample_values": sample_values.tolist()
                }
        
        return analysis
    
    def _looks_like_date(self, value: str) -> bool:
        """Check if a string looks like a date"""
        date_indicators = ['/', '-', ':', 'T', 'Z', 'AM', 'PM']
        return any(indicator in value for indicator in date_indicators) and len(value) > 8
    
    def _create_analysis_prompt(self, dataset_summary: Dict, user_query: str, problem_type: str) -> str:
        """Create prompt for dataset analysis"""
        
        return f"""
        You are an expert data scientist analyzing a dataset for a {problem_type} problem.
        
        User's Goal: "{user_query}"
        Problem Type: {problem_type}
        
        Dataset Summary:
        - Shape: {dataset_summary['shape']}
        - Columns: {dataset_summary['columns']}
        - Missing Values: {dataset_summary['missing_values']}
        - Data Types: {dataset_summary['dtypes']}
        - Sample Data: {dataset_summary['sample_data']}
        
        Please provide a comprehensive analysis in JSON format with the following structure:
        {{
            "data_quality_assessment": "Overall assessment of data quality",
            "target_variable_analysis": "Analysis of potential target variable based on user query",
            "data_quality_issues": [
                {{
                    "issue": "Description of issue",
                    "severity": "high/medium/low",
                    "impact": "How this affects the analysis",
                    "recommendation": "What to do about it"
                }}
            ],
            "feature_relevance": {{
                "highly_relevant": ["list of column names"],
                "moderately_relevant": ["list of column names"],
                "potentially_irrelevant": ["list of column names"]
            }},
            "feature_engineering_suggestions": [
                {{
                    "suggestion": "Description of feature engineering idea",
                    "reasoning": "Why this would be beneficial",
                    "implementation": "How to implement it",
                    "priority": "high/medium/low"
                }}
            ],
            "recommendations": [
                "List of actionable recommendations for data preparation"
            ]
        }}
        """
    
    def _create_cleaning_strategy_prompt(self, quality_issues: Dict, user_query: str, problem_type: str) -> str:
        """Create prompt for cleaning strategy"""
        
        return f"""
        You are an expert data scientist creating a data cleaning strategy for a {problem_type} problem.
        
        User's Goal: "{user_query}"
        Problem Type: {problem_type}
        
        Data Quality Issues Identified:
        {json.dumps(quality_issues, indent=2)}
        
        Please provide a detailed cleaning strategy in JSON format:
        {{
            "cleaning_strategy": {{
                "missing_values": {{
                    "strategy": "overall approach to missing values",
                    "column_specific": {{
                        "column_name": {{
                            "action": "drop/fill_median/fill_mode/interpolate/etc",
                            "reasoning": "why this approach for this column"
                        }}
                    }}
                }},
                "outliers": {{
                    "strategy": "overall approach to outliers",
                    "column_specific": {{
                        "column_name": {{
                            "action": "cap/remove/transform/keep",
                            "reasoning": "why this approach"
                        }}
                    }}
                }},
                "duplicates": {{
                    "action": "remove/keep",
                    "reasoning": "why this approach"
                }},
                "data_types": {{
                    "conversions": {{
                        "column_name": {{
                            "from": "current_type",
                            "to": "target_type",
                            "reasoning": "why convert"
                        }}
                    }}
                }}
            }},
            "expected_impact": "Description of how these changes will improve the analysis",
            "risks_and_considerations": ["List of potential risks or things to watch out for"],
            "quality_score_prediction": "Predicted data quality score after cleaning (0-100)"
        }}
        """
    
    def _create_feature_engineering_prompt(self, feature_analysis: Dict, user_query: str, problem_type: str) -> str:
        """Create prompt for feature engineering"""
        
        return f"""
        You are an expert data scientist suggesting feature engineering for a {problem_type} problem.
        
        User's Goal: "{user_query}"
        Problem Type: {problem_type}
        
        Current Feature Analysis:
        {json.dumps(feature_analysis, indent=2)}
        
        Please suggest intelligent feature engineering in JSON format:
        {{
            "feature_engineering_plan": [
                {{
                    "new_feature_name": "name of new feature",
                    "description": "what this feature represents",
                    "creation_method": "how to create it (formula/transformation)",
                    "source_columns": ["columns used to create this feature"],
                    "reasoning": "why this feature would be valuable for the problem",
                    "expected_impact": "how this might improve model performance",
                    "priority": "high/medium/low",
                    "complexity": "simple/medium/complex"
                }}
            ],
            "transformation_suggestions": [
                {{
                    "column": "column name",
                    "transformation": "log/sqrt/polynomial/standardize/etc",
                    "reasoning": "why this transformation is beneficial"
                }}
            ],
            "encoding_suggestions": [
                {{
                    "column": "categorical column name",
                    "encoding_method": "one_hot/label/target/etc",
                    "reasoning": "why this encoding method"
                }}
            ],
            "feature_selection_advice": "Advice on which features to keep/remove and why"
        }}
        """
    
    def _call_llm(self, prompt: str) -> str:
        """Call LLM with fallback logic"""

        # Check if any LLM client is available
        if not self.openai_client and not self.groq_client:
            logger.warning("No LLM clients available, using fallback response")
            return self._generate_fallback_response(prompt)

        try:
            # Try OpenAI first
            if self.openai_client:
                logger.info("Calling OpenAI API...")
                response = self.openai_client.chat.completions.create(
                    model=settings.DEFAULT_LLM_MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=2000
                )
                logger.info("OpenAI API call successful")
                return response.choices[0].message.content
        except Exception as e:
            logger.warning(f"OpenAI call failed: {e}")

        try:
            # Fallback to Groq
            if self.groq_client:
                logger.info("Calling Groq API...")
                response = self.groq_client.chat.completions.create(
                    model=settings.FALLBACK_LLM_MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=2000
                )
                logger.info("Groq API call successful")
                return response.choices[0].message.content
        except Exception as e:
            logger.warning(f"Groq call failed: {e}")

        # Final fallback - return structured response
        logger.info("Using fallback response generation")
        return self._generate_fallback_response(prompt)
    
    def _generate_fallback_response(self, prompt: str) -> str:
        """Generate intelligent fallback response when LLM calls fail"""

        if "analysis" in prompt.lower():
            return json.dumps({
                "data_quality_assessment": "Rule-based analysis completed - LLM analysis unavailable. Applied standard data science best practices for data quality assessment.",
                "target_variable_analysis": "Target variable identified based on user query keywords",
                "data_quality_issues": [
                    {
                        "issue": "Missing values detected",
                        "severity": "medium",
                        "impact": "May affect model performance if not handled properly",
                        "recommendation": "Use appropriate imputation strategy based on data type"
                    },
                    {
                        "issue": "Potential outliers in numeric columns",
                        "severity": "medium",
                        "impact": "Outliers can skew model predictions",
                        "recommendation": "Apply IQR-based outlier detection and capping"
                    }
                ],
                "feature_relevance": {
                    "highly_relevant": ["target_related_features"],
                    "moderately_relevant": ["numeric_features"],
                    "potentially_irrelevant": ["high_cardinality_categorical"]
                },
                "feature_engineering_suggestions": [
                    {
                        "suggestion": "Create ratio features for related numeric columns",
                        "reasoning": "Ratios often capture meaningful relationships",
                        "implementation": "Divide related numeric features",
                        "priority": "medium"
                    }
                ],
                "recommendations": [
                    "Apply standard data cleaning procedures",
                    "Handle missing values appropriately",
                    "Check for and handle outliers",
                    "Consider feature scaling for numeric variables"
                ]
            })
        elif "cleaning" in prompt.lower():
            return json.dumps({
                "cleaning_strategy": {
                    "missing_values": {
                        "strategy": "Apply type-appropriate imputation",
                        "column_specific": {
                            "numeric_columns": {
                                "action": "fill_median",
                                "reasoning": "Median is robust to outliers for numeric data"
                            },
                            "categorical_columns": {
                                "action": "fill_mode",
                                "reasoning": "Mode preserves the most common category"
                            }
                        }
                    },
                    "outliers": {
                        "strategy": "IQR-based outlier detection and capping",
                        "column_specific": {
                            "numeric_columns": {
                                "action": "cap",
                                "reasoning": "Capping preserves information while reducing extreme influence"
                            }
                        }
                    },
                    "duplicates": {
                        "action": "remove",
                        "reasoning": "Duplicate rows can bias model training and evaluation"
                    },
                    "data_types": {
                        "conversions": {
                            "categorical_as_numeric": {
                                "from": "object",
                                "to": "category",
                                "reasoning": "Categorical encoding improves memory efficiency"
                            }
                        }
                    }
                },
                "expected_impact": "Standard cleaning procedures will improve data quality and model performance",
                "risks_and_considerations": [
                    "Median imputation may not capture complex relationships",
                    "Outlier capping may remove legitimate extreme values",
                    "Consider domain expertise for better decisions"
                ],
                "quality_score_prediction": "85"
            })
        elif "feature" in prompt.lower():
            return json.dumps({
                "feature_engineering_plan": [
                    {
                        "new_feature_name": "ratio_feature_example",
                        "description": "Ratio of two related numeric features",
                        "creation_method": "divide feature1 by feature2",
                        "source_columns": ["feature1", "feature2"],
                        "reasoning": "Ratios often capture meaningful relationships between variables",
                        "expected_impact": "May improve model's ability to capture proportional relationships",
                        "priority": "medium",
                        "complexity": "simple"
                    }
                ],
                "transformation_suggestions": [
                    {
                        "column": "skewed_numeric_columns",
                        "transformation": "log",
                        "reasoning": "Log transformation can normalize skewed distributions"
                    }
                ],
                "encoding_suggestions": [
                    {
                        "column": "categorical_columns",
                        "encoding_method": "one_hot",
                        "reasoning": "One-hot encoding works well for low-cardinality categorical variables"
                    }
                ],
                "feature_selection_advice": "Use correlation analysis and feature importance to identify the most relevant features"
            })
        else:
            return json.dumps({
                "analysis": "Rule-based analysis completed - LLM unavailable",
                "recommendations": [
                    "Apply standard data science best practices",
                    "Review data quality manually",
                    "Consider domain expertise for feature engineering",
                    "Use statistical methods for outlier detection"
                ]
            })
    
    def _parse_text_response(self, response: str) -> Dict[str, Any]:
        """Parse text response when JSON parsing fails"""
        # Simple text parsing fallback
        return {
            "analysis": response,
            "recommendations": ["Review the analysis text for insights"]
        }
    
    def _parse_cleaning_strategy(self, response: str) -> Dict[str, Any]:
        """Parse cleaning strategy from text response"""
        return {
            "cleaning_strategy": {"text_response": response},
            "recommendations": ["Review the strategy text for guidance"]
        }
    
    def _parse_feature_engineering(self, response: str) -> Dict[str, Any]:
        """Parse feature engineering from text response"""
        return {
            "feature_engineering_plan": [],
            "text_suggestions": response,
            "recommendations": ["Review the suggestions text for ideas"]
        }
