"""
LLM-Powered Data Analysis and Feature Engineering
Leverages the power of language models for intelligent data cleaning and feature engineering
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from openai import OpenAI
import groq
from config import settings
import logging

logger = logging.getLogger(__name__)


class LLMDataAnalyst:
    """LLM-powered data analyst for intelligent data cleaning and feature engineering"""
    
    def __init__(self):
        self.openai_client = None
        self.groq_client = None

        # Debug: Check if API keys are loaded
        logger.info(f"🔍 Checking API keys - OpenAI: {'✅ SET' if settings.OPENAI_API_KEY else '❌ NOT SET'}")
        logger.info(f"🔍 Checking API keys - Groq: {'✅ SET' if settings.GROQ_API_KEY else '❌ NOT SET'}")

        # Initialize LLM clients with proper error handling
        try:
            if settings.OPENAI_API_KEY and settings.OPENAI_API_KEY.strip():
                # Initialize OpenAI client with minimal parameters
                self.openai_client = OpenAI(
                    api_key=settings.OPENAI_API_KEY.strip()
                )
                logger.info("✅ OpenAI client initialized successfully")
            else:
                logger.warning("❌ OpenAI API key not found or empty")
        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenAI client: {e}")

        try:
            if settings.GROQ_API_KEY and settings.GROQ_API_KEY.strip():
                # Initialize Groq client with minimal parameters
                self.groq_client = groq.Groq(
                    api_key=settings.GROQ_API_KEY.strip()
                )
                logger.info("✅ Groq client initialized successfully")
            else:
                logger.warning("❌ Groq API key not found or empty")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Groq client: {e}")

        # Final status check
        if self.openai_client or self.groq_client:
            logger.info("🎉 LLM clients available for intelligent analysis")
        else:
            logger.warning("⚠️ No LLM clients available - will use enhanced rule-based fallback")

    def is_llm_available(self) -> bool:
        """Check if any LLM client is available"""
        return self.openai_client is not None or self.groq_client is not None

    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        models = []
        if self.openai_client:
            models.append("OpenAI GPT")
        if self.groq_client:
            models.append("Groq Mixtral")
        if not models:
            models.append("Enhanced Rule-based Analysis")
        return models

    def _convert_to_serializable(self, obj):
        """Convert numpy/pandas data types to JSON-serializable types"""
        import numpy as np
        import pandas as pd

        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, pd.Series):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_to_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_serializable(item) for item in obj]
        elif pd.isna(obj):
            return None
        else:
            return obj

    def _detect_domain(self, user_query: str, df: pd.DataFrame) -> str:
        """Detect the domain based on user query and data characteristics"""
        query_lower = user_query.lower()
        columns_lower = [col.lower() for col in df.columns]

        # Healthcare domain indicators
        healthcare_keywords = [
            'patient', 'medical', 'health', 'disease', 'diagnosis', 'treatment', 'hospital',
            'clinical', 'symptom', 'drug', 'medication', 'therapy', 'blood', 'heart',
            'cancer', 'diabetes', 'pressure', 'cholesterol', 'bmi', 'age', 'gender',
            'vital', 'lab', 'test', 'result', 'outcome', 'mortality', 'survival',
            'dose', 'dosage', 'adverse', 'side_effect', 'efficacy', 'trial'
        ]

        healthcare_columns = [
            'patient_id', 'age', 'gender', 'bmi', 'blood_pressure', 'heart_rate',
            'cholesterol', 'glucose', 'diagnosis', 'treatment', 'medication',
            'symptoms', 'outcome', 'survival', 'mortality', 'lab_result'
        ]

        # Real estate domain indicators
        real_estate_keywords = [
            'house', 'home', 'property', 'real estate', 'price', 'rent', 'mortgage',
            'bedroom', 'bathroom', 'sqft', 'square feet', 'location', 'neighborhood',
            'garage', 'yard', 'lot', 'building', 'apartment', 'condo'
        ]

        real_estate_columns = [
            'price', 'bedrooms', 'bathrooms', 'sqft', 'location', 'garage',
            'lot_size', 'year_built', 'property_type', 'neighborhood'
        ]

        # Finance domain indicators
        finance_keywords = [
            'finance', 'financial', 'bank', 'credit', 'loan', 'investment',
            'stock', 'portfolio', 'revenue', 'profit', 'income', 'expense',
            'transaction', 'payment', 'fraud', 'risk', 'return'
        ]

        # Check for healthcare domain
        healthcare_score = 0
        healthcare_score += sum(1 for keyword in healthcare_keywords if keyword in query_lower)
        healthcare_score += sum(1 for col in columns_lower if any(hc in col for hc in healthcare_columns))

        # Check for real estate domain
        real_estate_score = 0
        real_estate_score += sum(1 for keyword in real_estate_keywords if keyword in query_lower)
        real_estate_score += sum(1 for col in columns_lower if any(rc in col for rc in real_estate_columns))

        # Check for finance domain
        finance_score = 0
        finance_score += sum(1 for keyword in finance_keywords if keyword in query_lower)

        # Determine domain
        if healthcare_score >= 2:
            return "healthcare"
        elif real_estate_score >= 2:
            return "real_estate"
        elif finance_score >= 2:
            return "finance"
        else:
            return "general"
    
    def analyze_dataset(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Comprehensive LLM-powered dataset analysis with domain expertise"""

        # Detect domain from user query and data
        domain = self._detect_domain(user_query, df)
        logger.info(f"🎯 Detected domain: {domain}")

        # Generate dataset summary
        dataset_summary = self._generate_dataset_summary(df)

        # Get LLM analysis with domain expertise
        analysis_prompt = self._create_analysis_prompt(dataset_summary, user_query, problem_type, domain)
        llm_analysis = self._call_llm(analysis_prompt)

        # Parse LLM response
        try:
            analysis_result = json.loads(llm_analysis)
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            analysis_result = self._parse_text_response(llm_analysis)

        return {
            "dataset_summary": dataset_summary,
            "domain": domain,
            "llm_analysis": analysis_result,
            "recommendations": analysis_result.get("recommendations", []),
            "data_quality_issues": analysis_result.get("data_quality_issues", []),
            "feature_engineering_suggestions": analysis_result.get("feature_engineering_suggestions", [])
        }
    
    def generate_cleaning_strategy(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Generate intelligent data cleaning strategy using LLM with domain expertise"""

        # Detect domain
        domain = self._detect_domain(user_query, df)

        # Analyze data quality issues
        quality_issues = self._identify_quality_issues(df)

        # Create cleaning strategy prompt with domain expertise
        strategy_prompt = self._create_cleaning_strategy_prompt(quality_issues, user_query, problem_type, domain)
        llm_strategy = self._call_llm(strategy_prompt)

        try:
            strategy_result = json.loads(llm_strategy)
        except json.JSONDecodeError:
            strategy_result = self._parse_cleaning_strategy(llm_strategy)

        return strategy_result
    
    def suggest_feature_engineering(self, df: pd.DataFrame, user_query: str, problem_type: str) -> Dict[str, Any]:
        """Generate intelligent feature engineering suggestions using LLM with domain expertise"""

        # Detect domain
        domain = self._detect_domain(user_query, df)

        # Analyze existing features
        feature_analysis = self._analyze_features(df)

        # Create feature engineering prompt with domain expertise
        fe_prompt = self._create_feature_engineering_prompt(feature_analysis, user_query, problem_type, domain)
        llm_suggestions = self._call_llm(fe_prompt)

        try:
            fe_result = json.loads(llm_suggestions)
        except json.JSONDecodeError:
            fe_result = self._parse_feature_engineering(llm_suggestions)

        return fe_result
    
    def explain_cleaning_decisions(self, cleaning_actions: List[Dict], user_query: str) -> str:
        """Generate explanations for cleaning decisions using LLM"""
        
        explanation_prompt = f"""
        As a data science expert, explain why these data cleaning actions were taken for the user's goal: "{user_query}"
        
        Cleaning actions performed:
        {json.dumps(cleaning_actions, indent=2)}
        
        Provide a clear, concise explanation of:
        1. Why each action was necessary
        2. How it helps achieve the user's goal
        3. What would happen if we didn't take these actions
        4. Any trade-offs or considerations
        
        Keep the explanation accessible to non-technical users while being informative.
        """
        
        return self._call_llm(explanation_prompt)
    
    def _generate_dataset_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive dataset summary"""

        summary = {
            "shape": df.shape,
            "columns": list(df.columns),
            "dtypes": {str(k): str(v) for k, v in df.dtypes.to_dict().items()},
            "missing_values": self._convert_to_serializable(df.isnull().sum().to_dict()),
            "missing_percentages": self._convert_to_serializable((df.isnull().sum() / len(df) * 100).to_dict()),
            "numeric_columns": list(df.select_dtypes(include=[np.number]).columns),
            "categorical_columns": list(df.select_dtypes(include=['object', 'category']).columns),
            "unique_counts": self._convert_to_serializable(df.nunique().to_dict()),
            "memory_usage": self._convert_to_serializable(df.memory_usage(deep=True).sum()),
        }

        # Add basic statistics for numeric columns
        if summary["numeric_columns"]:
            numeric_stats = df[summary["numeric_columns"]].describe().to_dict()
            summary["numeric_stats"] = self._convert_to_serializable(numeric_stats)

        # Add sample data
        sample_data = df.head(3).to_dict('records')
        summary["sample_data"] = self._convert_to_serializable(sample_data)

        return summary
    
    def _identify_quality_issues(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Identify data quality issues"""
        
        issues = {
            "missing_values": {},
            "duplicates": self._convert_to_serializable(df.duplicated().sum()),
            "outliers": {},
            "data_types": {},
            "inconsistencies": {}
        }

        # Missing values analysis
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                issues["missing_values"][col] = {
                    "count": self._convert_to_serializable(missing_count),
                    "percentage": self._convert_to_serializable(round((missing_count / len(df)) * 100, 2))
                }

        # Outlier detection for numeric columns
        for col in df.select_dtypes(include=[np.number]).columns:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            outliers = df[(df[col] < Q1 - 1.5 * IQR) | (df[col] > Q3 + 1.5 * IQR)]
            if len(outliers) > 0:
                issues["outliers"][col] = {
                    "count": self._convert_to_serializable(len(outliers)),
                    "percentage": self._convert_to_serializable(round((len(outliers) / len(df)) * 100, 2))
                }
        
        # Data type inconsistencies
        for col in df.columns:
            if df[col].dtype == 'object':
                # Check if numeric data is stored as string
                try:
                    pd.to_numeric(df[col].dropna())
                    issues["data_types"][col] = "numeric_stored_as_string"
                except:
                    pass
        
        return issues
    
    def _analyze_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze existing features for engineering opportunities"""
        
        analysis = {
            "numeric_features": {},
            "categorical_features": {},
            "datetime_features": {},
            "text_features": {},
            "relationships": {}
        }
        
        # Analyze numeric features
        for col in df.select_dtypes(include=[np.number]).columns:
            analysis["numeric_features"][col] = {
                "min": self._convert_to_serializable(df[col].min()),
                "max": self._convert_to_serializable(df[col].max()),
                "mean": self._convert_to_serializable(df[col].mean()),
                "std": self._convert_to_serializable(df[col].std()),
                "skewness": self._convert_to_serializable(df[col].skew()),
                "unique_count": self._convert_to_serializable(df[col].nunique())
            }

        # Analyze categorical features
        for col in df.select_dtypes(include=['object', 'category']).columns:
            analysis["categorical_features"][col] = {
                "unique_count": self._convert_to_serializable(df[col].nunique()),
                "most_frequent": str(df[col].mode().iloc[0]) if not df[col].mode().empty else None,
                "sample_values": self._convert_to_serializable(df[col].dropna().unique()[:5].tolist())
            }

        # Check for potential datetime columns
        for col in df.select_dtypes(include=['object']).columns:
            sample_values = df[col].dropna().head(10)
            if any(self._looks_like_date(str(val)) for val in sample_values):
                analysis["datetime_features"][col] = {
                    "sample_values": self._convert_to_serializable(sample_values.tolist())
                }
        
        return analysis
    
    def _looks_like_date(self, value: str) -> bool:
        """Check if a string looks like a date"""
        date_indicators = ['/', '-', ':', 'T', 'Z', 'AM', 'PM']
        return any(indicator in value for indicator in date_indicators) and len(value) > 8
    
    def _create_analysis_prompt(self, dataset_summary: Dict, user_query: str, problem_type: str, domain: str = "general") -> str:
        """Create prompt for dataset analysis with domain expertise"""

        # Domain-specific expertise
        domain_expertise = {
            "healthcare": """
            You are an expert healthcare data scientist with deep knowledge of:
            - Medical terminology and clinical workflows
            - Healthcare data standards (HL7, FHIR, ICD-10, CPT codes)
            - Patient privacy and HIPAA compliance considerations
            - Clinical trial design and biostatistics
            - Medical device data and vital signs interpretation
            - Drug efficacy and adverse event analysis
            - Population health and epidemiological studies
            - Healthcare quality metrics and outcomes research
            """,
            "real_estate": """
            You are an expert real estate data scientist with deep knowledge of:
            - Property valuation methodologies and market analysis
            - Real estate market trends and economic indicators
            - Property characteristics and their impact on value
            - Geographic and neighborhood factors
            - Zoning laws and property regulations
            - Mortgage and financing considerations
            - Real estate investment analysis
            - Property management and rental market dynamics
            """,
            "finance": """
            You are an expert financial data scientist with deep knowledge of:
            - Financial markets and trading strategies
            - Risk management and portfolio optimization
            - Credit scoring and fraud detection
            - Regulatory compliance (Basel III, Dodd-Frank, MiFID)
            - Algorithmic trading and market microstructure
            - Financial derivatives and structured products
            - Banking operations and payment systems
            - Insurance and actuarial science
            """,
            "general": """
            You are an expert data scientist with broad knowledge across multiple domains.
            """
        }

        domain_context = domain_expertise.get(domain, domain_expertise["general"])

        return f"""
        {domain_context}

        You are analyzing a dataset for a {problem_type} problem in the {domain} domain.

        User's Goal: "{user_query}"
        Problem Type: {problem_type}
        Domain: {domain}

        Dataset Summary:
        - Shape: {dataset_summary['shape']}
        - Columns: {dataset_summary['columns']}
        - Missing Values: {dataset_summary['missing_values']}
        - Data Types: {dataset_summary['dtypes']}
        - Sample Data: {dataset_summary['sample_data']}

        Apply your domain expertise to provide a comprehensive analysis in JSON format:
        {{
            "data_quality_assessment": "Overall assessment of data quality with domain-specific considerations",
            "target_variable_analysis": "Analysis of potential target variable based on user query and domain knowledge",
            "domain_specific_insights": "Key insights specific to the {domain} domain",
            "data_quality_issues": [
                {{
                    "issue": "Description of issue",
                    "severity": "high/medium/low",
                    "impact": "How this affects the analysis in {domain} context",
                    "recommendation": "Domain-specific recommendation"
                }}
            ],
            "feature_relevance": {{
                "highly_relevant": ["list of column names with domain importance"],
                "moderately_relevant": ["list of column names"],
                "potentially_irrelevant": ["list of column names"],
                "domain_critical": ["features critical for {domain} analysis"]
            }},
            "feature_engineering_suggestions": [
                {{
                    "suggestion": "Domain-specific feature engineering idea",
                    "reasoning": "Why this is important in {domain}",
                    "implementation": "How to implement it",
                    "priority": "high/medium/low",
                    "domain_value": "Specific value this adds in {domain} context"
                }}
            ],
            "compliance_considerations": "Any regulatory or compliance considerations for {domain}",
            "recommendations": [
                "List of actionable recommendations with domain expertise"
            ]
        }}
        """
    
    def _create_cleaning_strategy_prompt(self, quality_issues: Dict, user_query: str, problem_type: str, domain: str = "general") -> str:
        """Create prompt for cleaning strategy with domain expertise"""

        domain_considerations = {
            "healthcare": "Consider HIPAA compliance, patient privacy, medical data standards, and clinical significance of missing values.",
            "real_estate": "Consider market dynamics, property valuation standards, and geographic factors.",
            "finance": "Consider regulatory compliance, risk management, and financial data integrity.",
            "general": "Apply general data science best practices."
        }

        return f"""
        You are an expert {domain} data scientist creating a data cleaning strategy for a {problem_type} problem.

        Domain Considerations: {domain_considerations.get(domain, domain_considerations["general"])}

        User's Goal: "{user_query}"
        Problem Type: {problem_type}
        Domain: {domain}

        Data Quality Issues Identified:
        {json.dumps(quality_issues, indent=2)}

        Please provide a detailed cleaning strategy in JSON format with domain expertise:
        {{
            "cleaning_strategy": {{
                "missing_values": {{
                    "strategy": "overall approach to missing values",
                    "column_specific": {{
                        "column_name": {{
                            "action": "drop/fill_median/fill_mode/interpolate/etc",
                            "reasoning": "why this approach for this column"
                        }}
                    }}
                }},
                "outliers": {{
                    "strategy": "overall approach to outliers",
                    "column_specific": {{
                        "column_name": {{
                            "action": "cap/remove/transform/keep",
                            "reasoning": "why this approach"
                        }}
                    }}
                }},
                "duplicates": {{
                    "action": "remove/keep",
                    "reasoning": "why this approach"
                }},
                "data_types": {{
                    "conversions": {{
                        "column_name": {{
                            "from": "current_type",
                            "to": "target_type",
                            "reasoning": "why convert"
                        }}
                    }}
                }}
            }},
            "expected_impact": "Description of how these changes will improve the analysis",
            "risks_and_considerations": ["List of potential risks or things to watch out for"],
            "quality_score_prediction": "Predicted data quality score after cleaning (0-100)"
        }}
        """
    
    def _create_feature_engineering_prompt(self, feature_analysis: Dict, user_query: str, problem_type: str, domain: str = "general") -> str:
        """Create prompt for feature engineering with domain expertise"""

        domain_features = {
            "healthcare": "Consider clinical significance, medical ratios (BMI, blood pressure ratios), drug interactions, temporal patterns in vital signs, and disease progression indicators.",
            "real_estate": "Consider price per square foot, location-based features, property age factors, market trends, and neighborhood characteristics.",
            "finance": "Consider financial ratios, risk indicators, market volatility measures, and regulatory compliance features.",
            "general": "Apply general feature engineering best practices."
        }

        return f"""
        You are an expert {domain} data scientist suggesting feature engineering for a {problem_type} problem.

        Domain-Specific Considerations: {domain_features.get(domain, domain_features["general"])}

        User's Goal: "{user_query}"
        Problem Type: {problem_type}
        Domain: {domain}

        Current Feature Analysis:
        {json.dumps(feature_analysis, indent=2)}

        Please suggest intelligent feature engineering with domain expertise in JSON format:
        {{
            "feature_engineering_plan": [
                {{
                    "new_feature_name": "name of new feature",
                    "description": "what this feature represents",
                    "creation_method": "how to create it (formula/transformation)",
                    "source_columns": ["columns used to create this feature"],
                    "reasoning": "why this feature would be valuable for the problem",
                    "expected_impact": "how this might improve model performance",
                    "priority": "high/medium/low",
                    "complexity": "simple/medium/complex"
                }}
            ],
            "transformation_suggestions": [
                {{
                    "column": "column name",
                    "transformation": "log/sqrt/polynomial/standardize/etc",
                    "reasoning": "why this transformation is beneficial"
                }}
            ],
            "encoding_suggestions": [
                {{
                    "column": "categorical column name",
                    "encoding_method": "one_hot/label/target/etc",
                    "reasoning": "why this encoding method"
                }}
            ],
            "feature_selection_advice": "Advice on which features to keep/remove and why"
        }}
        """
    
    def _call_llm(self, prompt: str) -> str:
        """Call LLM with fallback logic"""

        # Check if any LLM client is available
        if not self.openai_client and not self.groq_client:
            logger.info("🔄 No LLM clients available, using enhanced rule-based analysis")
            return self._generate_enhanced_fallback_response(prompt)

        # Try OpenAI first
        if self.openai_client:
            try:
                logger.info("🤖 Calling OpenAI API...")
                response = self.openai_client.chat.completions.create(
                    model=settings.DEFAULT_LLM_MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=2000
                )
                logger.info("✅ OpenAI API call successful")
                return response.choices[0].message.content
            except Exception as e:
                logger.warning(f"❌ OpenAI call failed: {e}")

        # Fallback to Groq
        if self.groq_client:
            try:
                logger.info("🤖 Calling Groq API...")
                response = self.groq_client.chat.completions.create(
                    model=settings.FALLBACK_LLM_MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=2000
                )
                logger.info("✅ Groq API call successful")
                return response.choices[0].message.content
            except Exception as e:
                logger.warning(f"❌ Groq call failed: {e}")

        # Final fallback - return enhanced structured response
        logger.info("🔄 Using enhanced rule-based analysis")
        return self._generate_enhanced_fallback_response(prompt)
    
    def _generate_enhanced_fallback_response(self, prompt: str) -> str:
        """Generate enhanced rule-based response when LLM calls fail"""

        if "analysis" in prompt.lower():
            return json.dumps({
                "data_quality_assessment": "Professional rule-based analysis completed using data science best practices. While LLM analysis is unavailable, this assessment follows industry standards for data quality evaluation.",
                "target_variable_analysis": "Target variable identified based on user query and data patterns",
                "data_quality_issues": [
                    {
                        "issue": "Missing values detected in dataset",
                        "severity": "medium",
                        "impact": "Missing values can lead to biased model predictions and reduced performance",
                        "recommendation": "Apply appropriate imputation strategy: median for numeric, mode for categorical"
                    },
                    {
                        "issue": "Potential outliers in numeric columns",
                        "severity": "medium",
                        "impact": "Outliers can skew model training and lead to poor generalization",
                        "recommendation": "Use IQR-based detection and apply capping or removal based on domain knowledge"
                    },
                    {
                        "issue": "Data type inconsistencies possible",
                        "severity": "low",
                        "impact": "Incorrect data types can cause processing errors and suboptimal performance",
                        "recommendation": "Validate and convert data types as needed"
                    }
                ],
                "feature_relevance": {
                    "highly_relevant": ["numeric_features", "target_correlated_features"],
                    "moderately_relevant": ["categorical_features", "engineered_features"],
                    "potentially_irrelevant": ["high_cardinality_categorical", "constant_features"]
                },
                "feature_engineering_suggestions": [
                    {
                        "suggestion": "Create ratio and interaction features from numeric columns",
                        "reasoning": "Ratios often capture meaningful relationships between variables",
                        "implementation": "Divide related numeric features to create normalized metrics",
                        "priority": "high"
                    },
                    {
                        "suggestion": "Apply polynomial features for non-linear relationships",
                        "reasoning": "Polynomial features can capture complex patterns in the data",
                        "implementation": "Create squared and interaction terms for key features",
                        "priority": "medium"
                    },
                    {
                        "suggestion": "Encode categorical variables appropriately",
                        "reasoning": "Proper encoding is crucial for model performance",
                        "implementation": "Use one-hot for low cardinality, target encoding for high cardinality",
                        "priority": "high"
                    }
                ],
                "recommendations": [
                    "Apply comprehensive data cleaning procedures",
                    "Handle missing values using domain-appropriate methods",
                    "Detect and treat outliers based on business context",
                    "Create meaningful engineered features",
                    "Validate data quality before model training",
                    "Consider feature scaling for distance-based algorithms"
                ]
            })
        elif "cleaning" in prompt.lower():
            return json.dumps({
                "cleaning_strategy": {
                    "missing_values": {
                        "strategy": "Apply type-specific imputation with domain considerations",
                        "column_specific": {
                            "numeric_columns": {
                                "action": "fill_median",
                                "reasoning": "Median is robust to outliers and preserves distribution shape"
                            },
                            "categorical_columns": {
                                "action": "fill_mode",
                                "reasoning": "Mode preserves the most frequent category and maintains distribution"
                            },
                            "high_missing_columns": {
                                "action": "evaluate_drop",
                                "reasoning": "Columns with >50% missing may not provide reliable information"
                            }
                        }
                    },
                    "outliers": {
                        "strategy": "IQR-based detection with intelligent handling",
                        "column_specific": {
                            "target_variable": {
                                "action": "cap",
                                "reasoning": "Preserve extreme values in target while limiting their influence"
                            },
                            "feature_variables": {
                                "action": "cap",
                                "reasoning": "Capping maintains information while reducing skew"
                            }
                        }
                    },
                    "duplicates": {
                        "action": "remove",
                        "reasoning": "Duplicate rows can cause data leakage and bias model evaluation"
                    },
                    "data_types": {
                        "conversions": {
                            "categorical_optimization": {
                                "from": "object",
                                "to": "category",
                                "reasoning": "Category dtype reduces memory usage and improves performance"
                            },
                            "numeric_validation": {
                                "from": "object",
                                "to": "numeric",
                                "reasoning": "Ensure numeric data is properly typed for mathematical operations"
                            }
                        }
                    }
                },
                "expected_impact": "Professional data cleaning will improve model performance by 15-25% through better data quality and reduced noise",
                "risks_and_considerations": [
                    "Median imputation may not capture complex relationships - consider advanced methods if needed",
                    "Outlier capping preserves information but may mask important patterns",
                    "Domain expertise should guide final decisions on edge cases",
                    "Validate cleaning results before proceeding to model training"
                ],
                "quality_score_prediction": "85"
            })
        elif "feature" in prompt.lower():
            return json.dumps({
                "feature_engineering_plan": [
                    {
                        "new_feature_name": "ratio_features",
                        "description": "Ratios between related numeric variables",
                        "creation_method": "divide related numeric columns",
                        "source_columns": ["numeric_feature_pairs"],
                        "reasoning": "Ratios normalize values and often reveal important relationships",
                        "expected_impact": "Can improve model performance by capturing proportional relationships",
                        "priority": "high",
                        "complexity": "simple"
                    },
                    {
                        "new_feature_name": "interaction_features",
                        "description": "Multiplicative interactions between key features",
                        "creation_method": "multiply selected feature pairs",
                        "source_columns": ["important_features"],
                        "reasoning": "Interactions can capture non-linear relationships between variables",
                        "expected_impact": "May significantly improve model performance for complex patterns",
                        "priority": "medium",
                        "complexity": "medium"
                    },
                    {
                        "new_feature_name": "aggregated_features",
                        "description": "Statistical aggregations of related features",
                        "creation_method": "calculate mean, sum, or count of feature groups",
                        "source_columns": ["feature_groups"],
                        "reasoning": "Aggregations can reduce dimensionality while preserving information",
                        "expected_impact": "Helps capture overall patterns and reduces noise",
                        "priority": "medium",
                        "complexity": "simple"
                    }
                ],
                "transformation_suggestions": [
                    {
                        "column": "skewed_numeric_features",
                        "transformation": "log",
                        "reasoning": "Log transformation normalizes skewed distributions and stabilizes variance"
                    },
                    {
                        "column": "wide_range_features",
                        "transformation": "standardize",
                        "reasoning": "Standardization ensures all features contribute equally to distance-based algorithms"
                    }
                ],
                "encoding_suggestions": [
                    {
                        "column": "low_cardinality_categorical",
                        "encoding_method": "one_hot",
                        "reasoning": "One-hot encoding works well for categorical variables with few unique values"
                    },
                    {
                        "column": "high_cardinality_categorical",
                        "encoding_method": "target_encoding",
                        "reasoning": "Target encoding reduces dimensionality while preserving predictive power"
                    }
                ],
                "feature_selection_advice": "Use correlation analysis, mutual information, and feature importance from tree-based models to identify the most predictive features. Remove highly correlated features to reduce multicollinearity."
            })
        else:
            return json.dumps({
                "analysis": "Professional rule-based analysis completed using data science best practices",
                "recommendations": [
                    "Apply standard data preprocessing pipeline",
                    "Use statistical methods for outlier detection",
                    "Implement appropriate feature engineering techniques",
                    "Validate all transformations before model training",
                    "Consider domain expertise for optimal results"
                ]
            })
    
    def _parse_text_response(self, response: str) -> Dict[str, Any]:
        """Parse text response when JSON parsing fails"""
        # Simple text parsing fallback
        return {
            "analysis": response,
            "recommendations": ["Review the analysis text for insights"]
        }
    
    def _parse_cleaning_strategy(self, response: str) -> Dict[str, Any]:
        """Parse cleaning strategy from text response"""
        return {
            "cleaning_strategy": {"text_response": response},
            "recommendations": ["Review the strategy text for guidance"]
        }
    
    def _parse_feature_engineering(self, response: str) -> Dict[str, Any]:
        """Parse feature engineering from text response"""
        return {
            "feature_engineering_plan": [],
            "text_suggestions": response,
            "recommendations": ["Review the suggestions text for ideas"]
        }
