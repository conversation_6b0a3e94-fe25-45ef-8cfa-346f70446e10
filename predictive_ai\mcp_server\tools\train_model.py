"""
Single model training tool for the AI Data Science Pipeline
"""
import os
import sys
import pandas as pd
import numpy as np
import joblib
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    mean_squared_error, mean_absolute_error, r2_score
)
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.svm import SVC, SVR
import warnings
warnings.filterwarnings('ignore')

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, MODEL_CONFIGS, PROBLEM_TYPES
from mcp_server.models import ModelTrainingResult, ProblemType
from mcp_server.celery import task


class ModelTrainer:
    """Advanced model training service"""

    def __init__(self):
        self.model_save_dir = settings.MODEL_SAVE_DIR
        self.problem_types = PROBLEM_TYPES
        os.makedirs(self.model_save_dir, exist_ok=True)

    def train_model(
        self,
        dataset_path: str,
        problem_type: str,
        target_column: str,
        model_name: str = "random_forest",
        feature_columns: Optional[List[str]] = None,
        test_size: float = 0.2,
        random_state: int = 42
    ) -> Dict[str, Any]:
        """Train a single model"""
        logger.info(f"Training {model_name} model for {problem_type} problem")

        start_time = datetime.now()

        # Load and prepare data
        df = self._load_dataset(dataset_path)
        X, y = self._prepare_data(df, target_column, feature_columns)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )

        # Preprocess data
        X_train_processed, X_test_processed, preprocessor = self._preprocess_data(
            X_train, X_test, problem_type
        )

        # Train model
        model = self._get_model(model_name, problem_type)
        model.fit(X_train_processed, y_train)

        # Make predictions
        y_pred = model.predict(X_test_processed)

        # Calculate metrics
        metrics = self._calculate_metrics(y_test, y_pred, problem_type)

        # Get feature importance if available
        feature_importance = self._get_feature_importance(model, X.columns)

        # Save model
        model_id = str(uuid.uuid4())
        model_path = self._save_model(model, preprocessor, model_id, model_name)

        # Calculate training time
        training_time = (datetime.now() - start_time).total_seconds()

        # Generate training summary
        training_summary = self._generate_training_summary(
            model_name, problem_type, metrics, training_time, X.shape, y.nunique() if hasattr(y, 'nunique') else len(np.unique(y))
        )

        result = {
            "model_id": model_id,
            "model_name": model_name,
            "model_path": model_path,
            "training_time": training_time,
            "metrics": metrics,
            "feature_importance": feature_importance,
            "training_summary": training_summary
        }

        logger.info(f"Model training completed in {training_time:.2f} seconds")
        return result

    def _load_dataset(self, dataset_path: str) -> pd.DataFrame:
        """Load dataset from file"""
        try:
            if dataset_path.endswith('.csv'):
                df = pd.read_csv(dataset_path)
            elif dataset_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(dataset_path)
            elif dataset_path.endswith('.json'):
                df = pd.read_json(dataset_path)
            else:
                raise ValueError(f"Unsupported file format: {dataset_path}")

            logger.info(f"Loaded dataset with shape: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise

    def _prepare_data(self, df: pd.DataFrame, target_column: str, feature_columns: Optional[List[str]]) -> tuple:
        """Prepare features and target"""
        if target_column not in df.columns:
            raise ValueError(f"Target column '{target_column}' not found in dataset")

        # Get target
        y = df[target_column]

        # Get features
        if feature_columns:
            missing_cols = [col for col in feature_columns if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Feature columns not found: {missing_cols}")
            X = df[feature_columns]
        else:
            X = df.drop(columns=[target_column])

        logger.info(f"Prepared data: {X.shape[1]} features, {len(y)} samples")
        return X, y

    def _preprocess_data(self, X_train: pd.DataFrame, X_test: pd.DataFrame, problem_type: str) -> tuple:
        """Preprocess training and test data"""
        # Handle missing values
        numeric_columns = X_train.select_dtypes(include=[np.number]).columns
        categorical_columns = X_train.select_dtypes(include=['object']).columns

        # Create preprocessor
        preprocessor = {}

        # Numeric preprocessing
        if len(numeric_columns) > 0:
            numeric_imputer = SimpleImputer(strategy='median')
            scaler = StandardScaler()

            X_train_numeric = numeric_imputer.fit_transform(X_train[numeric_columns])
            X_test_numeric = numeric_imputer.transform(X_test[numeric_columns])

            X_train_numeric = scaler.fit_transform(X_train_numeric)
            X_test_numeric = scaler.transform(X_test_numeric)

            preprocessor['numeric_imputer'] = numeric_imputer
            preprocessor['scaler'] = scaler
        else:
            X_train_numeric = np.array([]).reshape(len(X_train), 0)
            X_test_numeric = np.array([]).reshape(len(X_test), 0)

        # Categorical preprocessing
        if len(categorical_columns) > 0:
            categorical_imputer = SimpleImputer(strategy='most_frequent')
            encoder = OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore')

            X_train_categorical = categorical_imputer.fit_transform(X_train[categorical_columns])
            X_test_categorical = categorical_imputer.transform(X_test[categorical_columns])

            X_train_categorical = encoder.fit_transform(X_train_categorical)
            X_test_categorical = encoder.transform(X_test_categorical)

            preprocessor['categorical_imputer'] = categorical_imputer
            preprocessor['encoder'] = encoder
        else:
            X_train_categorical = np.array([]).reshape(len(X_train), 0)
            X_test_categorical = np.array([]).reshape(len(X_test), 0)

        # Combine features
        X_train_processed = np.hstack([X_train_numeric, X_train_categorical])
        X_test_processed = np.hstack([X_test_numeric, X_test_categorical])

        logger.info(f"Preprocessed data shape: {X_train_processed.shape}")
        return X_train_processed, X_test_processed, preprocessor

    def _get_model(self, model_name: str, problem_type: str):
        """Get model instance based on name and problem type"""
        if problem_type == "classification":
            if model_name == "logistic_regression":
                return LogisticRegression(random_state=42, max_iter=1000)
            elif model_name == "random_forest":
                return RandomForestClassifier(n_estimators=100, random_state=42)
            elif model_name == "svm":
                return SVC(random_state=42, probability=True)
            else:
                return RandomForestClassifier(n_estimators=100, random_state=42)

        elif problem_type == "regression":
            if model_name == "linear_regression":
                return LinearRegression()
            elif model_name == "random_forest":
                return RandomForestRegressor(n_estimators=100, random_state=42)
            elif model_name == "svm":
                return SVR()
            else:
                return RandomForestRegressor(n_estimators=100, random_state=42)

        else:
            raise ValueError(f"Unsupported problem type: {problem_type}")

    def _calculate_metrics(self, y_true, y_pred, problem_type: str) -> Dict[str, float]:
        """Calculate appropriate metrics based on problem type"""
        metrics = {}

        if problem_type == "classification":
            metrics['accuracy'] = accuracy_score(y_true, y_pred)

            # Handle binary vs multiclass
            average = 'binary' if len(np.unique(y_true)) == 2 else 'weighted'

            metrics['precision'] = precision_score(y_true, y_pred, average=average, zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred, average=average, zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred, average=average, zero_division=0)

            # ROC AUC for binary classification
            if len(np.unique(y_true)) == 2:
                try:
                    metrics['roc_auc'] = roc_auc_score(y_true, y_pred)
                except:
                    metrics['roc_auc'] = 0.0

        elif problem_type == "regression":
            metrics['mse'] = mean_squared_error(y_true, y_pred)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2_score'] = r2_score(y_true, y_pred)

        return metrics

    def _get_feature_importance(self, model, feature_names) -> Optional[Dict[str, float]]:
        """Get feature importance if available"""
        try:
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
                return dict(zip(feature_names, importance.tolist()))
            elif hasattr(model, 'coef_'):
                # For linear models, use absolute coefficients
                coef = np.abs(model.coef_).flatten()
                return dict(zip(feature_names, coef.tolist()))
            else:
                return None
        except Exception as e:
            logger.warning(f"Could not extract feature importance: {e}")
            return None

    def _save_model(self, model, preprocessor, model_id: str, model_name: str) -> str:
        """Save trained model and preprocessor"""
        try:
            model_data = {
                'model': model,
                'preprocessor': preprocessor,
                'model_name': model_name,
                'model_id': model_id,
                'created_at': datetime.now().isoformat()
            }

            model_filename = f"{model_name}_{model_id}.joblib"
            model_path = os.path.join(self.model_save_dir, model_filename)

            joblib.dump(model_data, model_path)
            logger.info(f"Model saved to: {model_path}")

            return model_path
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise

    def _generate_training_summary(
        self,
        model_name: str,
        problem_type: str,
        metrics: Dict[str, float],
        training_time: float,
        data_shape: tuple,
        target_classes: int
    ) -> str:
        """Generate human-readable training summary"""
        summary_parts = []

        summary_parts.append(f"Successfully trained {model_name} model for {problem_type}")
        summary_parts.append(f"Training completed in {training_time:.2f} seconds")
        summary_parts.append(f"Dataset: {data_shape[0]} samples, {data_shape[1]} features")

        if problem_type == "classification":
            summary_parts.append(f"Number of classes: {target_classes}")
            if 'accuracy' in metrics:
                summary_parts.append(f"Test accuracy: {metrics['accuracy']:.4f}")
            if 'f1_score' in metrics:
                summary_parts.append(f"F1 score: {metrics['f1_score']:.4f}")

        elif problem_type == "regression":
            if 'r2_score' in metrics:
                summary_parts.append(f"R² score: {metrics['r2_score']:.4f}")
            if 'rmse' in metrics:
                summary_parts.append(f"RMSE: {metrics['rmse']:.4f}")

        return ". ".join(summary_parts)


# Celery task
@task(name="train_model")
def train_model_task(
    dataset_path: str,
    problem_type: str,
    target_column: str,
    model_name: str = "random_forest",
    feature_columns: Optional[List[str]] = None,
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Any]:
    """Celery task for model training"""
    try:
        logger.info(f"Starting model training task: {model_name}")

        trainer = ModelTrainer()
        result = trainer.train_model(
            dataset_path=dataset_path,
            problem_type=problem_type,
            target_column=target_column,
            model_name=model_name,
            feature_columns=feature_columns,
            test_size=test_size,
            random_state=random_state
        )

        logger.info("Model training task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in model training task: {e}")
        raise
