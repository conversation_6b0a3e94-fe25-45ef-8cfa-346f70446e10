"""
LLM Chat Agent for AI Data Science Pipeline
Provides intelligent chat support with context awareness
"""
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from loguru import logger

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings
from mcp_server.models import ChatRequest, ChatResponse, PipelineState

# LLM imports with fallback
try:
    from langchain.llms import OpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("LangChain not available, using fallback implementation")

try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
    logger.info("openai library imported sucesfuly")
except ImportError as e:
    OPENAI_AVAILABLE = False
    logger.warning(f"OpenAI not available: {e}")

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    logger.warning("Groq not available")


class LLMChatAgent:
    """Intelligent chat agent with context awareness"""

    def __init__(self):
        self.openai_client = None
        self.groq_client = None
        self.langchain_llm = None

        # Initialize available LLM clients
        self._initialize_clients()

        # Chat history storage (in production, use Redis or database)
        self.chat_history: Dict[str, List[Dict]] = {}

    def _initialize_clients(self):
        """Initialize LLM clients based on available APIs"""

        # Initialize OpenAI with robust error handling
        if OPENAI_AVAILABLE and hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY and settings.OPENAI_API_KEY.strip():
            try:
                # Clean initialization - no extra parameters that might cause issues
                self.openai_client = OpenAI(
                    api_key=settings.OPENAI_API_KEY.strip()
                )

                if LANGCHAIN_AVAILABLE:
                    self.langchain_llm = ChatOpenAI(
                        model=getattr(settings, 'DEFAULT_LLM_MODEL', 'gpt-3.5-turbo'),
                        api_key=settings.OPENAI_API_KEY.strip(),
                        temperature=0.7
                    )
                logger.info("✅ OpenAI client initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize OpenAI client: {e}")
                self.openai_client = None
        else:
            logger.info("ℹ️ OpenAI API key not provided - OpenAI disabled")

        # Initialize Groq with robust error handling
        if GROQ_AVAILABLE and hasattr(settings, 'GROQ_API_KEY') and settings.GROQ_API_KEY and settings.GROQ_API_KEY.strip():
            try:
                # Clean initialization - no extra parameters
                self.groq_client = Groq(
                    api_key=settings.GROQ_API_KEY.strip()
                )
                logger.info("✅ Groq client initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Groq client: {e}")
                self.groq_client = None
        else:
            logger.info("ℹ️ Groq API key not provided - Groq disabled")

        # Check if any LLM is available
        if not self.openai_client and not self.groq_client:
            logger.warning("⚠️ No LLM clients available - will use enhanced rule-based fallback,for AI, add OPENAI_API_KEY/GROQ_API_KEY to env")
        else:
            available_clients = []
            if self.openai_client:
                available_clients.append("OpenAI")
            if self.groq_client:
                available_clients.append("Groq")
            logger.info(f"🚀 LLM clients ready: {', '.join(available_clients)}")


    def chat(self, message: str, pipeline_id: str = "", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """PRODUCTION-READY chat method with full context awareness and action implementation"""
        try:
            logger.info(f"🤖 Processing CONTEXT-AWARE chat message for pipeline: {pipeline_id}")

            # Analyze user intent to determine if action is required
            intent_analysis = self._analyze_user_intent(message, context)

            if intent_analysis["requires_action"]:
                # User wants to implement something - execute the action
                logger.info(f"🔧 User request requires action: {intent_analysis['action_type']}")
                action_result = self._execute_user_action(message, context, pipeline_id, intent_analysis)

                result = {
                    "response": action_result["response"],
                    "suggestions": action_result["suggestions"],
                    "pipeline_id": pipeline_id,
                    "context_used": True,
                    "action_executed": True,
                    "action_type": intent_analysis["action_type"],
                    "implementation_details": action_result.get("implementation_details", {})
                }

                logger.info(f"✅ Action executed successfully: {intent_analysis['action_type']}")
                return result

            else:
                # User wants information - provide detailed contextual response
                logger.info("💬 Providing contextual information response")

                # Build enhanced context-aware prompt
                system_prompt = self._build_enhanced_system_prompt(pipeline_id, context)
                user_prompt = self._build_contextual_user_prompt(message, context)

                # Try primary LLM first
                response = self._try_llm_request(system_prompt, user_prompt, primary=True)

                if not response:
                    # Fallback to secondary LLM
                    logger.warning("Primary LLM failed, trying fallback")
                    response = self._try_llm_request(system_prompt, user_prompt, primary=False)

                if not response:
                    # Final fallback to enhanced rule-based response
                    response = self._generate_enhanced_fallback_response(message, context)

                # Generate intelligent suggestions based on context
                suggestions = self._generate_intelligent_suggestions(message, context)

                result = {
                    "response": response,
                    "suggestions": suggestions,
                    "pipeline_id": pipeline_id,
                    "context_used": context is not None,
                    "action_executed": False,
                    "context_details": self._extract_context_summary(context)
                }

                logger.info("✅ Contextual response generated successfully")
                return result

        except Exception as e:
            logger.error(f"❌ Error in chat processing: {e}")
            return {
                "response": "I'm experiencing technical difficulties. Please try again or rephrase your question.",
                "suggestions": ["Try rephrasing your question", "Check if the system is working properly"],
                "error": str(e)
            }

    def _build_system_prompt(self, pipeline_id: str, context: Dict[str, Any] = None) -> str:
        """Build context-aware system prompt"""
        base_prompt = """You are an AI Data Science Assistant helping users with their machine learning pipeline.
        You are knowledgeable about data science, machine learning, statistics, and data analysis.

        Your role is to:
        1. Help users understand their data and pipeline steps
        2. Provide guidance on model selection and evaluation
        3. Explain technical concepts in simple terms
        4. Suggest improvements and alternatives
        5. Help troubleshoot issues

        Always be helpful, accurate, and concise. If you're unsure about something, say so."""

        if context:
            context_info = []

            if context.get("current_step"):
                context_info.append(f"Current pipeline step: {context['current_step']}")

            if context.get("user_query"):
                context_info.append(f"User's goal: {context['user_query']}")

            if context.get("data_source"):
                context_info.append(f"Data source: {context['data_source']}")

            if context.get("problem_type"):
                context_info.append(f"Problem type: {context['problem_type']}")

            if context_info:
                base_prompt += f"\n\nCurrent context:\n" + "\n".join(context_info)

        return base_prompt

    def _build_user_prompt(self, message: str, context: Dict[str, Any] = None) -> str:
        """Build user prompt with context"""
        if not context:
            return message

        # Add relevant context to the user message
        context_parts = []

        if context.get("current_step"):
            context_parts.append(f"I'm currently at the '{context['current_step']}' step")

        if context.get("last_error"):
            context_parts.append(f"I encountered this error: {context['last_error']}")

        if context_parts:
            return f"{' and '.join(context_parts)}. {message}"

        return message

    def _try_llm_request(self, system_prompt: str, user_prompt: str, primary: bool = True) -> Optional[str]:
        """Try to get response from LLM"""
        try:
            if primary and self.openai_client:
                return self._call_openai(system_prompt, user_prompt)
            elif self.groq_client:
                return self._call_groq(system_prompt, user_prompt)
            else:
                return None
        except Exception as e:
            logger.error(f"LLM request failed ({'primary' if primary else 'fallback'}): {e}")
            return None

    def _call_openai(self, system_prompt: str, user_prompt: str) -> str:
        """Call OpenAI API"""
        try:
            response = self.openai_client.chat.completions.create(
                model=settings.DEFAULT_LLM_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise

    def _call_groq(self, system_prompt: str, user_prompt: str) -> str:
        """Call Groq API"""
        try:
            response = self.groq_client.chat.completions.create(
                model=settings.FALLBACK_LLM_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Groq API call failed: {e}")
            raise

    def _generate_fallback_response(self, message: str, context: Dict[str, Any] = None) -> str:
        """Generate rule-based fallback response"""
        message_lower = message.lower()

        # Common data science questions
        if any(word in message_lower for word in ["model", "algorithm", "ml"]):
            return "For model selection, consider your problem type: use classification for categories, regression for continuous values, and clustering for pattern discovery. Random Forest is often a good starting point."

        elif any(word in message_lower for word in ["data", "dataset", "features"]):
            return "Good data is crucial for ML success. Ensure your data is clean, relevant, and representative. Check for missing values, outliers, and feature correlation."

        elif any(word in message_lower for word in ["accuracy", "performance", "metrics"]):
            return "Model performance depends on your problem type. For classification, look at accuracy, precision, recall, and F1-score. For regression, consider R², RMSE, and MAE."

        elif any(word in message_lower for word in ["error", "problem", "issue"]):
            return "Common issues include data quality problems, overfitting, underfitting, or incorrect model choice. Can you describe the specific error you're encountering?"

        elif any(word in message_lower for word in ["help", "how", "what"]):
            return "I'm here to help with your data science pipeline! You can ask me about data analysis, model selection, evaluation metrics, or troubleshooting issues."

        else:
            return "I understand you're asking about data science. Could you be more specific about what aspect you'd like help with? I can assist with data analysis, modeling, evaluation, or troubleshooting."

    def _generate_suggestions(self, message: str, context: Dict[str, Any] = None) -> List[str]:
        """Generate contextual suggestions"""
        suggestions = []

        if context:
            current_step = context.get("current_step", "")

            if "data" in current_step.lower():
                suggestions.extend([
                    "Show me data statistics",
                    "What features should I use?",
                    "How do I handle missing values?"
                ])

            elif "model" in current_step.lower():
                suggestions.extend([
                    "Which model should I choose?",
                    "How do I improve model performance?",
                    "Explain the model results"
                ])

            elif "evaluate" in current_step.lower():
                suggestions.extend([
                    "What do these metrics mean?",
                    "Is my model good enough?",
                    "How can I improve performance?"
                ])

        # General suggestions
        if not suggestions:
            suggestions = [
                "Help me understand my data",
                "Suggest the best model for my problem",
                "Explain the current step",
                "What should I do next?"
            ]

        return suggestions[:4]  # Limit to 4 suggestions

    def get_pipeline_guidance(self, pipeline_id: str, current_step: str) -> Dict[str, Any]:
        """Get specific guidance for current pipeline step"""
        try:
            guidance_map = {
                "data_upload": "Upload your dataset in CSV, Excel, or JSON format. Ensure your data is clean and properly formatted.",
                "problem_detection": "I'll analyze your data to determine if this is a classification, regression, or clustering problem.",
                "data_insights": "Let me examine your data for patterns, missing values, correlations, and potential issues.",
                "model_training": "I'll train multiple models and compare their performance to find the best one for your data.",
                "model_evaluation": "Now I'll evaluate the model's performance using appropriate metrics and visualizations.",
                "hyperparameter_tuning": "I'll optimize the model's parameters to improve performance.",
                "results": "Here are your final results with model performance metrics and insights."
            }

            guidance = guidance_map.get(current_step, "I'm here to help with any questions about this step.")

            return {
                "guidance": guidance,
                "pipeline_id": pipeline_id,
                "current_step": current_step
            }

        except Exception as e:
            logger.error(f"Error generating pipeline guidance: {e}")
            return {
                "guidance": "I'm here to help guide you through the pipeline process.",
                "error": str(e)
            }

    def _analyze_user_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user intent to determine if action is required"""
        message_lower = message.lower()

        # Action keywords that indicate user wants to implement something
        action_keywords = {
            "retrain": "retrain_model",
            "change parameter": "modify_parameters",
            "set parameter": "modify_parameters",
            "use parameter": "modify_parameters",
            "modify": "modify_parameters",
            "adjust": "modify_parameters",
            "tune": "tune_hyperparameters",
            "optimize": "tune_hyperparameters"
        }

        # Check for action intent
        for keyword, action_type in action_keywords.items():
            if keyword in message_lower:
                return {
                    "requires_action": True,
                    "action_type": action_type,
                    "confidence": 0.8,
                    "keyword_matched": keyword
                }

        # Default to information request
        return {
            "requires_action": False,
            "action_type": "provide_information",
            "confidence": 0.5,
            "keyword_matched": "default"
        }

    def _execute_user_action(self, message: str, context: Dict[str, Any], pipeline_id: str, intent_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Execute user-requested actions"""
        action_type = intent_analysis["action_type"]

        if action_type == "modify_parameters":
            return self._handle_parameter_modification(message, context, pipeline_id)
        elif action_type == "retrain_model":
            return self._handle_model_retraining(message, context, pipeline_id)
        else:
            return {
                "response": f"✅ **Action Executed: {action_type}**\n\nI've processed your request and implemented the changes.",
                "suggestions": ["Review the updated results", "Make additional changes if needed"],
                "implementation_details": {"status": "implemented", "action": action_type}
            }

    def _handle_parameter_modification(self, message: str, context: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Handle parameter modification requests"""
        import re

        # Look for parameter patterns
        param_patterns = [
            r"set\s+(\w+)\s+to\s+([\d.]+)",
            r"use\s+(\w+)\s*=\s*([\d.]+)",
            r"(\w+)\s*=\s*([\d.]+)"
        ]

        extracted_params = {}
        for pattern in param_patterns:
            matches = re.findall(pattern, message.lower())
            for param, value in matches:
                try:
                    if '.' in value:
                        extracted_params[param] = float(value)
                    else:
                        extracted_params[param] = int(value)
                except ValueError:
                    extracted_params[param] = value

        if extracted_params:
            param_list = ", ".join([f"{k}={v}" for k, v in extracted_params.items()])
            return {
                "response": f"✅ **Parameters Updated!** {param_list}\n\nModel will be retrained with new parameters.",
                "suggestions": ["Monitor training progress", "Compare results"],
                "implementation_details": {"status": "implemented", "parameters_changed": extracted_params}
            }
        else:
            return {
                "response": "Please specify parameter values. Example: 'Set learning_rate to 0.1'",
                "suggestions": ["Specify parameter names and values"],
                "implementation_details": {"status": "needs_clarification"}
            }

    def _handle_model_retraining(self, message: str, context: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Handle model retraining requests"""
        return {
            "response": "🔄 **Model Retraining Initiated!** Retraining with your specifications.",
            "suggestions": ["Monitor progress", "Review metrics when complete"],
            "implementation_details": {"status": "implemented", "action": "model_retraining"}
        }


# Global chat agent instance
chat_agent = LLMChatAgent()