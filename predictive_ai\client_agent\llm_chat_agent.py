"""
LLM Chat Agent for AI Data Science Pipeline
Provides intelligent chat support with context awareness
"""
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from loguru import logger

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings
from mcp_server.models import ChatRequest, ChatResponse, PipelineState

# LLM imports with fallback
try:
    from langchain.llms import OpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("LangChain not available, using fallback implementation")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI not available")

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    logger.warning("Groq not available")


class LLMChatAgent:
    """Intelligent chat agent with context awareness"""

    def __init__(self):
        self.openai_client = None
        self.groq_client = None
        self.langchain_llm = None

        # Initialize available LLM clients
        self._initialize_clients()

        # Chat history storage (in production, use Redis or database)
        self.chat_history: Dict[str, List[Dict]] = {}

    def _initialize_clients(self):
        """Initialize LLM clients based on available APIs"""

        # Initialize OpenAI
        if OPENAI_AVAILABLE and settings.OPENAI_API_KEY:
            try:
                openai.api_key = settings.OPENAI_API_KEY
                self.openai_client = openai

                if LANGCHAIN_AVAILABLE:
                    self.langchain_llm = ChatOpenAI(
                        model_name=settings.DEFAULT_LLM_MODEL,
                        openai_api_key=settings.OPENAI_API_KEY,
                        temperature=0.7
                    )
                logger.info("OpenAI client initialized")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI: {e}")

        # Initialize Groq as fallback
        if GROQ_AVAILABLE and settings.GROQ_API_KEY:
            try:
                self.groq_client = Groq(api_key=settings.GROQ_API_KEY)
                logger.info("Groq client initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Groq: {e}")