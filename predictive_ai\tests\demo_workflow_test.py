"""
DEMO WORKFLOW TEST
Demonstrates the complete AI chat workflow with realistic scenarios
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import tempfile

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from client_agent.chatgpt_ds_assistant import ChatGPTDataScienceAssistant


def create_realistic_dataset():
    """Create a realistic house price dataset"""
    print("🏠 Creating realistic house price dataset...")
    
    np.random.seed(42)
    n_samples = 500
    
    # Generate realistic house data
    zipcodes = ['90210', '10001', '60601', '30309', '94102']
    data = {
        'zipcode': np.random.choice(zipcodes, n_samples),
        'price': np.random.normal(300000, 100000, n_samples),
        'bedrooms': np.random.choice([1, 2, 3, 4, 5], n_samples, p=[0.1, 0.2, 0.4, 0.2, 0.1]),
        'bathrooms': np.random.choice([1, 2, 3, 4], n_samples, p=[0.2, 0.4, 0.3, 0.1]),
        'sqft': np.random.normal(1800, 600, n_samples),
        'age': np.random.randint(0, 50, n_samples),
        'garage': np.random.choice([0, 1, 2], n_samples, p=[0.3, 0.5, 0.2]),
        'pool': np.random.choice([0, 1], n_samples, p=[0.8, 0.2])
    }
    
    # Make price correlate with features
    df = pd.DataFrame(data)
    df['price'] = (
        df['price'] + 
        df['bedrooms'] * 20000 + 
        df['bathrooms'] * 15000 + 
        df['sqft'] * 100 + 
        (50 - df['age']) * 1000 +
        df['garage'] * 10000 +
        df['pool'] * 25000
    )
    
    # Add some missing values
    missing_indices = np.random.choice(n_samples, 30, replace=False)
    df.loc[missing_indices, 'sqft'] = np.nan
    
    missing_indices = np.random.choice(n_samples, 20, replace=False)
    df.loc[missing_indices, 'age'] = np.nan
    
    # Add some outliers
    outlier_indices = np.random.choice(n_samples, 10, replace=False)
    df.loc[outlier_indices, 'price'] *= 3
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    df.to_csv(temp_file.name, index=False)
    temp_file.close()
    
    print(f"✅ Dataset created: {df.shape} with {df.isnull().sum().sum()} missing values")
    print(f"📊 Price range: ${df['price'].min():,.0f} - ${df['price'].max():,.0f}")
    print(f"💾 Saved to: {temp_file.name}")
    
    return temp_file.name, df


def demo_data_cleaning_workflow():
    """Demo the data cleaning workflow"""
    print("\n" + "="*60)
    print("🧹 DEMO: DATA CLEANING WORKFLOW")
    print("="*60)
    
    dataset_path, df = create_realistic_dataset()
    chat_assistant = ChatGPTDataScienceAssistant()
    
    # Create context
    context = {
        'pipeline_id': 'demo_pipeline',
        'current_step': 'data_cleaning',
        'problem_type': 'regression',
        'step_data': {
            'dataset_path': dataset_path,
            'original_shape': df.shape,
            'target_column': 'price'
        }
    }
    
    # Scenario 1: User asks about missing values
    print("\n📋 Scenario 1: User asks about missing values")
    print("-" * 40)
    
    message = "what should I do about missing values in the dataset?"
    response = chat_assistant.chat(message, 'demo_pipeline', context)
    
    print(f"🤖 AI Response: {response['response'][:200]}...")
    print(f"💡 Suggestions: {response['suggestions'][:2]}")
    
    # Scenario 2: User implements missing value handling
    print("\n📋 Scenario 2: User implements missing value handling")
    print("-" * 40)
    
    message = "fill missing values with median"
    response = chat_assistant.chat(message, 'demo_pipeline', context)
    
    print(f"🤖 AI Response: {response['response']}")
    if response.get('implementation_completed'):
        print("✅ Implementation completed successfully!")
        impl_details = response.get('implementation_details', {})
        print(f"📊 Details: {impl_details.get('description', 'No details')}")
    
    # Scenario 3: User creates a new feature
    print("\n📋 Scenario 3: User creates a new feature")
    print("-" * 40)
    
    message = "create a column by grouping zipcode and calculating average price"
    response = chat_assistant.chat(message, 'demo_pipeline', context)
    
    print(f"🤖 AI Response: {response['response']}")
    if response.get('implementation_completed'):
        print("✅ Feature engineering completed!")
        impl_details = response.get('implementation_details', {})
        if 'new_dataset_path' in impl_details:
            new_df = pd.read_csv(impl_details['new_dataset_path'])
            print(f"📊 New dataset shape: {new_df.shape}")
            print(f"📋 New columns: {list(new_df.columns)}")


def demo_rejection_alternatives():
    """Demo the rejection alternatives workflow"""
    print("\n" + "="*60)
    print("🔄 DEMO: REJECTION ALTERNATIVES WORKFLOW")
    print("="*60)
    
    dataset_path, df = create_realistic_dataset()
    chat_assistant = ChatGPTDataScienceAssistant()
    
    # Mock step data for data cleaning
    step_data = {
        'dataset_path': dataset_path,
        'original_shape': df.shape,
        'cleaning_actions': ['remove_duplicates', 'handle_missing']
    }
    
    context = {
        'pipeline_id': 'demo_pipeline',
        'current_step': 'data_cleaning',
        'problem_type': 'regression'
    }
    
    # Generate alternatives for data cleaning rejection
    print("\n📋 Generating alternatives for rejected data cleaning step...")
    alternatives = chat_assistant.generate_rejection_alternatives(
        'data_cleaning', step_data, context
    )
    
    print(f"✅ Generated {len(alternatives)} alternatives:")
    
    for i, alt in enumerate(alternatives, 1):
        print(f"\n{i}. **{alt['title']}**")
        print(f"   Type: {alt['action_type']}")
        print(f"   Description: {alt['description']}")
        
        # Show implementation config
        if 'implementation' in alt:
            impl = alt['implementation']
            print(f"   Implementation: {impl['type']} -> {impl['operation']}")
    
    # Demo executing an alternative
    print(f"\n📋 Executing alternative: {alternatives[0]['title']}")
    print("-" * 40)
    
    result = chat_assistant.execute_alternative(alternatives[0], step_data, 'demo_pipeline')
    
    if result['success']:
        print("✅ Alternative executed successfully!")
        print(f"📊 Description: {result['description']}")
        print(f"📈 Result: {result.get('result_summary', 'No summary')}")
    else:
        print(f"❌ Alternative execution failed: {result.get('error')}")


def demo_natural_language_requests():
    """Demo various natural language requests"""
    print("\n" + "="*60)
    print("🗣️ DEMO: NATURAL LANGUAGE REQUESTS")
    print("="*60)
    
    dataset_path, df = create_realistic_dataset()
    chat_assistant = ChatGPTDataScienceAssistant()
    
    context = {
        'pipeline_id': 'demo_pipeline',
        'current_step': 'feature_engineering',
        'problem_type': 'regression',
        'step_data': {
            'dataset_path': dataset_path,
            'original_shape': df.shape,
            'models_trained': [
                {
                    'model_name': 'Random Forest',
                    'performance_score': 0.85,
                    'parameters': {'n_estimators': 100, 'max_depth': 10}
                }
            ]
        }
    }
    
    # Test various natural language requests
    test_requests = [
        "apply standard scaling to all numeric features",
        "create polynomial features of degree 2",
        "remove outliers using the IQR method",
        "set learning_rate to 0.05",
        "what's the performance of the trained models?",
        "explain what features are most important"
    ]
    
    for i, request in enumerate(test_requests, 1):
        print(f"\n📋 Request {i}: '{request}'")
        print("-" * 50)
        
        response = chat_assistant.chat(request, 'demo_pipeline', context)
        
        print(f"🤖 Response Type: {'Implementation' if response.get('implementation_completed') else 'Information'}")
        print(f"📝 Response: {response['response'][:150]}...")
        
        if response.get('implementation_completed'):
            print("✅ Implementation completed!")
            impl_details = response.get('implementation_details', {})
            if impl_details.get('description'):
                print(f"🔧 What was done: {impl_details['description']}")
        
        if response.get('suggestions'):
            print(f"💡 Suggestions: {response['suggestions'][:2]}")


def demo_conversation_memory():
    """Demo conversation memory functionality"""
    print("\n" + "="*60)
    print("🧠 DEMO: CONVERSATION MEMORY")
    print("="*60)
    
    chat_assistant = ChatGPTDataScienceAssistant()
    pipeline_id = 'memory_demo'
    
    # Simulate a conversation
    conversation = [
        ("Hello, I need help with my dataset", "Hi! I'm here to help with your data science project."),
        ("I want to create a new feature", "I can help you create features. What kind of feature do you need?"),
        ("Group by zipcode and calculate average price", "I'll create a zipcode average price feature for you."),
        ("What did we just discuss?", "We discussed creating a feature by grouping zipcode and calculating average price.")
    ]
    
    print("📋 Simulating conversation...")
    
    for i, (user_msg, ai_msg) in enumerate(conversation, 1):
        print(f"\n{i}. User: {user_msg}")
        
        # Store in memory
        chat_assistant._store_conversation(pipeline_id, user_msg, ai_msg)
        
        print(f"   AI: {ai_msg}")
    
    # Check memory
    memory = chat_assistant.conversation_memory.get(pipeline_id, [])
    print(f"\n✅ Conversation memory contains {len(memory)} exchanges")
    
    for i, exchange in enumerate(memory, 1):
        print(f"{i}. User: {exchange['user'][:50]}...")
        print(f"   AI: {exchange['ai'][:50]}...")


def main():
    """Run all demo workflows"""
    print("🎭 AI CHAT WORKFLOW DEMONSTRATION")
    print("=" * 60)
    print("This demo shows the complete AI chat workflow with realistic scenarios")
    
    try:
        demo_data_cleaning_workflow()
        demo_rejection_alternatives()
        demo_natural_language_requests()
        demo_conversation_memory()
        
        print("\n" + "=" * 60)
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("✅ All workflows demonstrated successfully")
        print("🚀 System is production-ready!")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
