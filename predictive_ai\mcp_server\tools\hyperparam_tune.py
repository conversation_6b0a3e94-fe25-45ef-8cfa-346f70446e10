"""
Hyperparameter tuning tool for the AI Data Science Pipeline
"""
import os
import sys
import pandas as pd
import numpy as np
import joblib
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.svm import SVC, SVR
from sklearn.metrics import make_scorer, accuracy_score, f1_score, r2_score
import warnings
warnings.filterwarnings('ignore')

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings
from mcp_server.celery import task


class HyperparameterTuner:
    """Advanced hyperparameter tuning service"""

    def __init__(self):
        self.model_save_dir = settings.MODEL_SAVE_DIR
        os.makedirs(self.model_save_dir, exist_ok=True)

    def tune_hyperparameters(
        self,
        dataset_path: str,
        problem_type: str,
        target_column: str,
        model_name: str = "random_forest",
        feature_columns: Optional[List[str]] = None,
        search_method: str = "grid",  # "grid" or "random"
        cv_folds: int = 5,
        n_iter: int = 50,  # For random search
        test_size: float = 0.2,
        random_state: int = 42
    ) -> Dict[str, Any]:
        """Tune hyperparameters for a model"""
        logger.info(f"Starting hyperparameter tuning for {model_name} ({problem_type})")

        start_time = datetime.now()

        # Load and prepare data
        df = self._load_dataset(dataset_path)
        X, y = self._prepare_data(df, target_column, feature_columns)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )

        # Preprocess data
        X_train_processed, X_test_processed, preprocessor = self._preprocess_data(
            X_train, X_test, problem_type
        )

        # Get model and parameter grid
        model = self._get_base_model(model_name, problem_type)
        param_grid = self._get_parameter_grid(model_name, problem_type)

        # Get scoring metric
        scoring = self._get_scoring_metric(problem_type)

        # Perform hyperparameter search
        if search_method == "grid":
            search = GridSearchCV(
                model, param_grid, cv=cv_folds, scoring=scoring,
                n_jobs=-1, verbose=1
            )
        else:  # random search
            search = RandomizedSearchCV(
                model, param_grid, cv=cv_folds, scoring=scoring,
                n_iter=n_iter, n_jobs=-1, verbose=1, random_state=random_state
            )

        # Fit the search
        logger.info(f"Starting {search_method} search with {cv_folds}-fold CV")
        search.fit(X_train_processed, y_train)

        # Get best model and evaluate
        best_model = search.best_estimator_
        best_params = search.best_params_
        best_score = search.best_score_

        # Evaluate on test set
        test_score = best_model.score(X_test_processed, y_test)

        # Save the best model
        model_id = f"tuned_{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        model_path = self._save_tuned_model(
            best_model, preprocessor, model_id, model_name, best_params
        )

        # Calculate tuning time
        tuning_time = (datetime.now() - start_time).total_seconds()

        # Generate tuning summary
        tuning_summary = self._generate_tuning_summary(
            model_name, search_method, best_score, test_score,
            tuning_time, len(param_grid)
        )

        result = {
            "model_id": model_id,
            "model_name": model_name,
            "model_path": model_path,
            "best_parameters": best_params,
            "best_cv_score": best_score,
            "test_score": test_score,
            "tuning_time": tuning_time,
            "search_method": search_method,
            "cv_results": self._extract_cv_results(search),
            "tuning_summary": tuning_summary
        }

        logger.info(f"Hyperparameter tuning completed in {tuning_time:.2f} seconds")
        return result

    def _load_dataset(self, dataset_path: str) -> pd.DataFrame:
        """Load dataset from file"""
        try:
            if dataset_path.endswith('.csv'):
                df = pd.read_csv(dataset_path)
            elif dataset_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(dataset_path)
            elif dataset_path.endswith('.json'):
                df = pd.read_json(dataset_path)
            else:
                raise ValueError(f"Unsupported file format: {dataset_path}")

            logger.info(f"Loaded dataset with shape: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise

    def _prepare_data(self, df: pd.DataFrame, target_column: str, feature_columns: Optional[List[str]]) -> tuple:
        """Prepare features and target"""
        if target_column not in df.columns:
            raise ValueError(f"Target column '{target_column}' not found in dataset")

        # Get target
        y = df[target_column]

        # Get features
        if feature_columns:
            missing_cols = [col for col in feature_columns if col not in df.columns]
            if missing_cols:
                raise ValueError(f"Feature columns not found: {missing_cols}")
            X = df[feature_columns]
        else:
            X = df.drop(columns=[target_column])

        return X, y

    def _preprocess_data(self, X_train: pd.DataFrame, X_test: pd.DataFrame, problem_type: str) -> tuple:
        """Preprocess training and test data"""
        # Handle missing values and scaling (simplified version)
        numeric_columns = X_train.select_dtypes(include=[np.number]).columns
        categorical_columns = X_train.select_dtypes(include=['object']).columns

        preprocessor = {}

        # Numeric preprocessing
        if len(numeric_columns) > 0:
            numeric_imputer = SimpleImputer(strategy='median')
            scaler = StandardScaler()

            X_train_numeric = numeric_imputer.fit_transform(X_train[numeric_columns])
            X_test_numeric = numeric_imputer.transform(X_test[numeric_columns])

            X_train_numeric = scaler.fit_transform(X_train_numeric)
            X_test_numeric = scaler.transform(X_test_numeric)

            preprocessor['numeric_imputer'] = numeric_imputer
            preprocessor['scaler'] = scaler
        else:
            X_train_numeric = np.array([]).reshape(len(X_train), 0)
            X_test_numeric = np.array([]).reshape(len(X_test), 0)

        # Categorical preprocessing
        if len(categorical_columns) > 0:
            categorical_imputer = SimpleImputer(strategy='most_frequent')
            encoder = OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore')

            X_train_categorical = categorical_imputer.fit_transform(X_train[categorical_columns])
            X_test_categorical = categorical_imputer.transform(X_test[categorical_columns])

            X_train_categorical = encoder.fit_transform(X_train_categorical)
            X_test_categorical = encoder.transform(X_test_categorical)

            preprocessor['categorical_imputer'] = categorical_imputer
            preprocessor['encoder'] = encoder
        else:
            X_train_categorical = np.array([]).reshape(len(X_train), 0)
            X_test_categorical = np.array([]).reshape(len(X_test), 0)

        # Combine features
        X_train_processed = np.hstack([X_train_numeric, X_train_categorical])
        X_test_processed = np.hstack([X_test_numeric, X_test_categorical])

        return X_train_processed, X_test_processed, preprocessor

    def _get_base_model(self, model_name: str, problem_type: str):
        """Get base model for tuning"""
        if problem_type == "classification":
            if model_name == "logistic_regression":
                return LogisticRegression(random_state=42, max_iter=1000)
            elif model_name == "random_forest":
                return RandomForestClassifier(random_state=42)
            elif model_name == "svm":
                return SVC(random_state=42)
            else:
                return RandomForestClassifier(random_state=42)

        elif problem_type == "regression":
            if model_name == "linear_regression":
                return LinearRegression()
            elif model_name == "random_forest":
                return RandomForestRegressor(random_state=42)
            elif model_name == "svm":
                return SVR()
            else:
                return RandomForestRegressor(random_state=42)

        else:
            raise ValueError(f"Unsupported problem type: {problem_type}")

    def _get_parameter_grid(self, model_name: str, problem_type: str) -> Dict[str, List]:
        """Get parameter grid for hyperparameter search"""
        if problem_type == "classification":
            if model_name == "logistic_regression":
                return {
                    'C': [0.1, 1, 10, 100],
                    'penalty': ['l1', 'l2'],
                    'solver': ['liblinear', 'saga']
                }
            elif model_name == "random_forest":
                return {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [None, 10, 20, 30],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                }
            elif model_name == "svm":
                return {
                    'C': [0.1, 1, 10, 100],
                    'kernel': ['rbf', 'linear'],
                    'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
                }

        elif problem_type == "regression":
            if model_name == "linear_regression":
                return {
                    'fit_intercept': [True, False],
                    'normalize': [True, False]  # Note: deprecated in newer sklearn versions
                }
            elif model_name == "random_forest":
                return {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [None, 10, 20, 30],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                }
            elif model_name == "svm":
                return {
                    'C': [0.1, 1, 10, 100],
                    'kernel': ['rbf', 'linear'],
                    'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
                }

        # Default fallback
        return {'random_state': [42]}

    def _get_scoring_metric(self, problem_type: str) -> str:
        """Get appropriate scoring metric"""
        if problem_type == "classification":
            return 'f1_weighted'
        elif problem_type == "regression":
            return 'r2'
        else:
            return 'accuracy'

    def _save_tuned_model(
        self,
        model,
        preprocessor,
        model_id: str,
        model_name: str,
        best_params: Dict[str, Any]
    ) -> str:
        """Save tuned model"""
        try:
            model_data = {
                'model': model,
                'preprocessor': preprocessor,
                'model_name': model_name,
                'model_id': model_id,
                'best_parameters': best_params,
                'tuned': True,
                'created_at': datetime.now().isoformat()
            }

            model_filename = f"tuned_{model_name}_{model_id}.joblib"
            model_path = os.path.join(self.model_save_dir, model_filename)

            joblib.dump(model_data, model_path)
            logger.info(f"Tuned model saved to: {model_path}")

            return model_path
        except Exception as e:
            logger.error(f"Error saving tuned model: {e}")
            raise

    def _extract_cv_results(self, search) -> Dict[str, Any]:
        """Extract useful cross-validation results"""
        try:
            cv_results = search.cv_results_

            # Get top 5 parameter combinations
            top_indices = np.argsort(cv_results['rank_test_score'])[:5]

            top_results = []
            for idx in top_indices:
                result = {
                    'rank': int(cv_results['rank_test_score'][idx]),
                    'mean_test_score': float(cv_results['mean_test_score'][idx]),
                    'std_test_score': float(cv_results['std_test_score'][idx]),
                    'params': cv_results['params'][idx]
                }
                top_results.append(result)

            return {
                'top_5_results': top_results,
                'best_score': float(search.best_score_),
                'total_combinations_tested': len(cv_results['params'])
            }
        except Exception as e:
            logger.warning(f"Error extracting CV results: {e}")
            return {}

    def _generate_tuning_summary(
        self,
        model_name: str,
        search_method: str,
        best_cv_score: float,
        test_score: float,
        tuning_time: float,
        param_combinations: int
    ) -> str:
        """Generate tuning summary"""
        summary_parts = []

        summary_parts.append(f"Hyperparameter tuning completed for {model_name}")
        summary_parts.append(f"Search method: {search_method} search")
        summary_parts.append(f"Best cross-validation score: {best_cv_score:.4f}")
        summary_parts.append(f"Test set score: {test_score:.4f}")
        summary_parts.append(f"Tuning time: {tuning_time:.2f} seconds")
        summary_parts.append(f"Parameter combinations tested: {param_combinations}")

        # Performance improvement indicator
        score_diff = test_score - best_cv_score
        if abs(score_diff) < 0.01:
            summary_parts.append("Model performance is consistent between CV and test sets")
        elif score_diff > 0.01:
            summary_parts.append("Model performs better on test set than expected from CV")
        else:
            summary_parts.append("Model may be overfitting (CV score > test score)")

        return ". ".join(summary_parts)


# Celery task
@task(name="hyperparam_tune")
def hyperparam_tune_task(
    dataset_path: str,
    problem_type: str,
    target_column: str,
    model_name: str = "random_forest",
    feature_columns: Optional[List[str]] = None,
    search_method: str = "grid",
    cv_folds: int = 5,
    n_iter: int = 50,
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Any]:
    """Celery task for hyperparameter tuning"""
    try:
        logger.info("Starting hyperparameter tuning task")

        tuner = HyperparameterTuner()
        result = tuner.tune_hyperparameters(
            dataset_path=dataset_path,
            problem_type=problem_type,
            target_column=target_column,
            model_name=model_name,
            feature_columns=feature_columns,
            search_method=search_method,
            cv_folds=cv_folds,
            n_iter=n_iter,
            test_size=test_size,
            random_state=random_state
        )

        logger.info("Hyperparameter tuning task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in hyperparameter tuning task: {e}")
        raise


# Celery task
@task(name="hyperparam_tune")
def hyperparam_tune_task(*args, **kwargs) -> Dict[str, Any]:
    """Celery task for the service"""
    try:
        logger.info("Starting task")

        service = HyperparameterTuner()
        result = service.tune_hyperparameters(*args, **kwargs)

        logger.info("Task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in task: {e}")
        raise
