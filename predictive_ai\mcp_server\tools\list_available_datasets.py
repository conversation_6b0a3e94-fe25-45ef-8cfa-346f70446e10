"""
List available datasets tool for the AI Data Science Pipeline
"""
import os
import sys
from typing import List, Dict, Any
from loguru import logger

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings
from mcp_server.models import DatasetInfo
from mcp_server.celery import task
from mcp_server.tools.suggest_datasets import DatasetSuggester


class DatasetLister:
    """Dataset listing service"""

    def __init__(self):
        self.suggester = DatasetSuggester()

    def list_all_datasets(self) -> List[DatasetInfo]:
        """List all available datasets"""
        return self.suggester.get_available_datasets()

    def list_datasets_by_type(self, source_type: str) -> List[DatasetInfo]:
        """List datasets by source type"""
        all_datasets = self.list_all_datasets()
        return [ds for ds in all_datasets if ds.source_type.value == source_type]

    def list_datasets_by_size(self, size_category: str) -> List[DatasetInfo]:
        """List datasets by size category (small, medium, large)"""
        all_datasets = self.list_all_datasets()
        filtered_datasets = []

        for dataset in all_datasets:
            if not dataset.num_rows:
                continue

            if size_category == "small" and dataset.num_rows <= 1000:
                filtered_datasets.append(dataset)
            elif size_category == "medium" and 1000 < dataset.num_rows <= 100000:
                filtered_datasets.append(dataset)
            elif size_category == "large" and dataset.num_rows > 100000:
                filtered_datasets.append(dataset)

        return filtered_datasets

    def get_dataset_details(self, dataset_name: str) -> DatasetInfo:
        """Get detailed information about a specific dataset"""
        all_datasets = self.list_all_datasets()
        for dataset in all_datasets:
            if dataset.name == dataset_name:
                return dataset
        raise ValueError(f"Dataset '{dataset_name}' not found")


# Celery task
@task(name="list_available_datasets")
def list_available_datasets_task(
    source_type: str = None,
    size_category: str = None,
    dataset_name: str = None
) -> Dict[str, Any]:
    """Celery task for listing available datasets"""
    try:
        logger.info("Starting dataset listing")

        lister = DatasetLister()

        if dataset_name:
            # Get specific dataset details
            dataset = lister.get_dataset_details(dataset_name)
            result = {
                "datasets": [{
                    "name": dataset.name,
                    "description": dataset.description,
                    "source_type": dataset.source_type.value,
                    "file_path": dataset.file_path,
                    "table_name": dataset.table_name,
                    "num_rows": dataset.num_rows,
                    "num_columns": dataset.num_columns,
                    "column_names": dataset.column_names,
                    "column_types": dataset.column_types,
                    "size_mb": dataset.size_mb
                }],
                "total_count": 1,
                "filter_applied": f"dataset_name={dataset_name}"
            }
        elif source_type:
            # Filter by source type
            datasets = lister.list_datasets_by_type(source_type)
            result = {
                "datasets": [
                    {
                        "name": ds.name,
                        "description": ds.description,
                        "source_type": ds.source_type.value,
                        "file_path": ds.file_path,
                        "table_name": ds.table_name,
                        "num_rows": ds.num_rows,
                        "num_columns": ds.num_columns,
                        "column_names": ds.column_names,
                        "column_types": ds.column_types,
                        "size_mb": ds.size_mb
                    }
                    for ds in datasets
                ],
                "total_count": len(datasets),
                "filter_applied": f"source_type={source_type}"
            }
        elif size_category:
            # Filter by size
            datasets = lister.list_datasets_by_size(size_category)
            result = {
                "datasets": [
                    {
                        "name": ds.name,
                        "description": ds.description,
                        "source_type": ds.source_type.value,
                        "file_path": ds.file_path,
                        "table_name": ds.table_name,
                        "num_rows": ds.num_rows,
                        "num_columns": ds.num_columns,
                        "column_names": ds.column_names,
                        "column_types": ds.column_types,
                        "size_mb": ds.size_mb
                    }
                    for ds in datasets
                ],
                "total_count": len(datasets),
                "filter_applied": f"size_category={size_category}"
            }
        else:
            # List all datasets
            datasets = lister.list_all_datasets()
            result = {
                "datasets": [
                    {
                        "name": ds.name,
                        "description": ds.description,
                        "source_type": ds.source_type.value,
                        "file_path": ds.file_path,
                        "table_name": ds.table_name,
                        "num_rows": ds.num_rows,
                        "num_columns": ds.num_columns,
                        "column_names": ds.column_names,
                        "column_types": ds.column_types,
                        "size_mb": ds.size_mb
                    }
                    for ds in datasets
                ],
                "total_count": len(datasets),
                "filter_applied": "none"
            }

        logger.info(f"Dataset listing completed successfully. Found {result['total_count']} datasets")
        return result

    except Exception as e:
        logger.error(f"Error in dataset listing: {e}")
        raise