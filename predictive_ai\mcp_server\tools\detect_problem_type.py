"""
Problem type detection tool for the AI Data Science Pipeline
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from loguru import logger
import sys

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import ProblemDetectionResult, ProblemType
from mcp_server.celery import task


class ProblemTypeDetector:
    """Intelligent problem type detection service"""

    def __init__(self):
        self.problem_types = PROBLEM_TYPES

    def detect_problem_type(self, dataset_path: str, target_column: Optional[str] = None) -> ProblemDetectionResult:
        """Detect the problem type for a dataset"""
        logger.info(f"Detecting problem type for: {dataset_path}")

        # Load dataset
        df = self._load_dataset(dataset_path)

        # If no target column specified, try to infer it
        if not target_column:
            target_column = self._infer_target_column(df)

        # Analyze the target column and dataset
        analysis = self._analyze_dataset(df, target_column)

        # Detect problem type based on analysis
        detected_type, confidence, reasoning = self._classify_problem_type(analysis)

        # Get feature columns (all except target)
        feature_columns = [col for col in df.columns if col != target_column]

        # Generate alternative problem types
        alternatives = self._generate_alternatives(analysis, detected_type)

        return ProblemDetectionResult(
            detected_problem_type=detected_type,
            confidence_score=confidence,
            reasoning=reasoning,
            target_column=target_column,
            feature_columns=feature_columns,
            alternative_problem_types=alternatives,
            dataset_analysis=analysis
        )

    def _load_dataset(self, dataset_path: str) -> pd.DataFrame:
        """Load dataset from file path"""
        try:
            if dataset_path.endswith('.csv'):
                df = pd.read_csv(dataset_path)
            elif dataset_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(dataset_path)
            elif dataset_path.endswith('.json'):
                df = pd.read_json(dataset_path)
            else:
                raise ValueError(f"Unsupported file format: {dataset_path}")

            logger.info(f"Loaded dataset with shape: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"Error loading dataset {dataset_path}: {e}")
            raise

    def _infer_target_column(self, df: pd.DataFrame) -> str:
        """Infer the most likely target column"""
        # Common target column names
        target_keywords = [
            'target', 'label', 'class', 'y', 'output', 'result', 'outcome',
            'price', 'value', 'amount', 'score', 'rating', 'prediction',
            'churn', 'fraud', 'default', 'success', 'failure'
        ]

        # Check for exact matches first
        for col in df.columns:
            if col.lower() in target_keywords:
                return col

        # Check for partial matches
        for col in df.columns:
            for keyword in target_keywords:
                if keyword in col.lower():
                    return col

        # If no obvious target, use the last column
        return df.columns[-1]

    def _analyze_dataset(self, df: pd.DataFrame, target_column: str) -> Dict[str, Any]:
        """Analyze dataset and target column characteristics"""
        analysis = {}

        # Basic dataset info
        analysis['dataset_shape'] = df.shape
        analysis['column_count'] = len(df.columns)
        analysis['row_count'] = len(df)

        # Target column analysis
        if target_column in df.columns:
            target_series = df[target_column]

            analysis['target_column'] = target_column
            analysis['target_dtype'] = str(target_series.dtype)
            analysis['target_unique_count'] = target_series.nunique()
            analysis['target_null_count'] = target_series.isnull().sum()
            analysis['target_unique_ratio'] = target_series.nunique() / len(target_series)

            # Check if target is numeric
            analysis['target_is_numeric'] = pd.api.types.is_numeric_dtype(target_series)

            # Check if target is categorical
            if analysis['target_is_numeric']:
                # For numeric targets, check if they're discrete (integers)
                analysis['target_is_discrete'] = target_series.dtype in ['int64', 'int32'] or target_series.apply(lambda x: x == int(x) if pd.notnull(x) else True).all()
                analysis['target_range'] = (target_series.min(), target_series.max())
            else:
                analysis['target_is_discrete'] = True
                analysis['target_categories'] = target_series.value_counts().to_dict()

        # Feature analysis
        feature_columns = [col for col in df.columns if col != target_column]
        analysis['feature_columns'] = feature_columns
        analysis['feature_count'] = len(feature_columns)

        # Analyze feature types
        numeric_features = []
        categorical_features = []
        datetime_features = []

        for col in feature_columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                numeric_features.append(col)
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                datetime_features.append(col)
            else:
                categorical_features.append(col)

        analysis['numeric_features'] = numeric_features
        analysis['categorical_features'] = categorical_features
        analysis['datetime_features'] = datetime_features

        # Check for time series indicators
        analysis['has_datetime_features'] = len(datetime_features) > 0
        analysis['has_time_keywords'] = any(
            keyword in ' '.join(df.columns).lower()
            for keyword in ['date', 'time', 'year', 'month', 'day', 'timestamp']
        )

        return analysis

    def _classify_problem_type(self, analysis: Dict[str, Any]) -> tuple[ProblemType, float, str]:
        """Classify the problem type based on analysis"""
        target_unique_ratio = analysis.get('target_unique_ratio', 0)
        target_is_numeric = analysis.get('target_is_numeric', False)
        target_is_discrete = analysis.get('target_is_discrete', False)
        target_unique_count = analysis.get('target_unique_count', 0)
        has_datetime_features = analysis.get('has_datetime_features', False)
        has_time_keywords = analysis.get('has_time_keywords', False)

        reasoning_parts = []

        # Time series detection
        if has_datetime_features or has_time_keywords:
            reasoning_parts.append("Dataset contains temporal features")
            return ProblemType.TIME_SERIES, 0.8, "Dataset contains datetime features or time-related columns, suggesting time series analysis."

        # Classification vs Regression
        if target_is_numeric:
            if target_is_discrete and target_unique_count <= 10:
                # Likely classification with numeric labels
                reasoning_parts.append(f"Target has {target_unique_count} unique discrete values")
                return ProblemType.CLASSIFICATION, 0.9, f"Target column has {target_unique_count} unique discrete values, indicating classification."
            elif target_unique_ratio < 0.05:
                # Low unique ratio suggests classification
                reasoning_parts.append(f"Target has low unique ratio ({target_unique_ratio:.3f})")
                return ProblemType.CLASSIFICATION, 0.8, f"Target column has low unique value ratio ({target_unique_ratio:.3f}), suggesting classification."
            else:
                # Continuous numeric target suggests regression
                reasoning_parts.append("Target is continuous numeric")
                return ProblemType.REGRESSION, 0.9, "Target column is continuous numeric, indicating regression problem."
        else:
            # Non-numeric target is classification
            reasoning_parts.append(f"Target is categorical with {target_unique_count} categories")
            return ProblemType.CLASSIFICATION, 0.95, f"Target column is categorical with {target_unique_count} categories."

        # Default fallback
        return ProblemType.CLASSIFICATION, 0.5, "Unable to determine problem type with high confidence, defaulting to classification."

    def _generate_alternatives(self, analysis: Dict[str, Any], detected_type: ProblemType) -> List[Dict[str, Any]]:
        """Generate alternative problem types with reasoning"""
        alternatives = []

        target_is_numeric = analysis.get('target_is_numeric', False)
        target_unique_count = analysis.get('target_unique_count', 0)
        has_datetime_features = analysis.get('has_datetime_features', False)

        # Always suggest clustering as an unsupervised alternative
        alternatives.append({
            "problem_type": ProblemType.CLUSTERING.value,
            "confidence": 0.6,
            "reasoning": "Unsupervised clustering to discover patterns in the data"
        })

        if detected_type != ProblemType.REGRESSION and target_is_numeric:
            alternatives.append({
                "problem_type": ProblemType.REGRESSION.value,
                "confidence": 0.7,
                "reasoning": "Target is numeric, could be treated as regression problem"
            })

        if detected_type != ProblemType.CLASSIFICATION:
            if target_unique_count <= 20:
                alternatives.append({
                    "problem_type": ProblemType.CLASSIFICATION.value,
                    "confidence": 0.8,
                    "reasoning": f"Target has {target_unique_count} unique values, suitable for classification"
                })

        if detected_type != ProblemType.TIME_SERIES and has_datetime_features:
            alternatives.append({
                "problem_type": ProblemType.TIME_SERIES.value,
                "confidence": 0.7,
                "reasoning": "Dataset contains datetime features, could be time series analysis"
            })

        return alternatives


# Celery task
@task(name="detect_problem_type")
def detect_problem_type_task(dataset_path: str, target_column: Optional[str] = None) -> Dict[str, Any]:
    """Celery task for problem type detection"""
    try:
        logger.info(f"Starting problem type detection for: {dataset_path}")

        detector = ProblemTypeDetector()
        result = detector.detect_problem_type(dataset_path, target_column)

        # Convert to dict for JSON serialization
        result_dict = {
            "detected_problem_type": result.detected_problem_type.value,
            "confidence_score": result.confidence_score,
            "reasoning": result.reasoning,
            "target_column": result.target_column,
            "feature_columns": result.feature_columns,
            "alternative_problem_types": result.alternative_problem_types,
            "dataset_analysis": result.dataset_analysis
        }

        logger.info(f"Problem type detection completed: {result.detected_problem_type.value}")
        return result_dict

    except Exception as e:
        logger.error(f"Error in problem type detection: {e}")
        raise