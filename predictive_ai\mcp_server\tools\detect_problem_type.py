"""
Problem type detection tool for the AI Data Science Pipeline
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from loguru import logger
import sys

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import ProblemDetectionResult, ProblemType
from mcp_server.celery import task


class ProblemTypeDetector:
    """Intelligent problem type detection service"""

    def __init__(self):
        self.problem_types = PROBLEM_TYPES

    def detect_problem_type(self, dataset_path: str, target_column: Optional[str] = None) -> ProblemDetectionResult:
        """Detect the problem type for a dataset"""
        logger.info(f"Detecting problem type for: {dataset_path}")

        # Load dataset
        df = self._load_dataset(dataset_path)

        # If no target column specified, try to infer it
        if not target_column:
            target_column = self._infer_target_column(df)

        # Analyze the target column and dataset
        analysis = self._analyze_dataset(df, target_column)

        # Detect problem type based on analysis
        detected_type, confidence, reasoning = self._classify_problem_type(analysis)

        # Get feature columns (all except target)
        feature_columns = [col for col in df.columns if col != target_column]

        # Generate alternative problem types
        alternatives = self._generate_alternatives(analysis, detected_type)

        return ProblemDetectionResult(
            detected_problem_type=detected_type,
            confidence_score=confidence,
            reasoning=reasoning,
            target_column=target_column,
            feature_columns=feature_columns,
            alternative_problem_types=alternatives
        )