"""
Dataset suggestion tool for the AI Data Science Pipeline
"""
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from loguru import logger
import sqlalchemy as sa
from sqlalchemy import create_engine, text
import sys

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import DatasetInfo, DataSourceType, ProblemType
from mcp_server.celery import task


class DatasetSuggester:
    """Dataset suggestion engine"""

    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        self.db_engine = None
        if settings.DATABASE_URL:
            try:
                self.db_engine = create_engine(settings.DATABASE_URL)
            except Exception as e:
                logger.warning(f"Could not connect to database: {e}")

    def analyze_user_query(self, query: str) -> Dict[str, Any]:
        """Analyze user query to extract intent and requirements"""
        query_lower = query.lower()

        # Detect problem type from query
        problem_type = None
        if any(word in query_lower for word in ["predict", "forecast", "estimate", "regression"]):
            problem_type = ProblemType.REGRESSION
        elif any(word in query_lower for word in ["classify", "categorize", "class", "classification"]):
            problem_type = ProblemType.CLASSIFICATION
        elif any(word in query_lower for word in ["time series", "temporal", "trend", "seasonal"]):
            problem_type = ProblemType.TIME_SERIES
        elif any(word in query_lower for word in ["cluster", "group", "segment", "similar"]):
            problem_type = ProblemType.CLUSTERING

        # Extract domain keywords
        domain_keywords = []
        domains = {
            "finance": ["price", "stock", "financial", "money", "revenue", "profit"],
            "healthcare": ["medical", "health", "patient", "disease", "treatment"],
            "retail": ["sales", "customer", "product", "purchase", "order"],
            "real_estate": ["house", "property", "real estate", "rent", "apartment"],
            "marketing": ["campaign", "advertisement", "conversion", "click"],
            "sports": ["game", "player", "team", "score", "match"],
            "weather": ["temperature", "weather", "climate", "precipitation"],
            "education": ["student", "grade", "school", "university", "course"]
        }

        for domain, keywords in domains.items():
            if any(keyword in query_lower for keyword in keywords):
                domain_keywords.append(domain)

        return {
            "problem_type": problem_type,
            "domain_keywords": domain_keywords,
            "query": query
        }

    def get_available_datasets(self) -> List[DatasetInfo]:
        """Get all available datasets from files and database"""
        datasets = []

        # Get datasets from uploaded files
        if os.path.exists(self.upload_dir):
            for filename in os.listdir(self.upload_dir):
                if filename.endswith(('.csv', '.xlsx', '.xls')):
                    file_path = os.path.join(self.upload_dir, filename)
                    try:
                        dataset_info = self._analyze_file_dataset(file_path, filename)
                        datasets.append(dataset_info)
                    except Exception as e:
                        logger.error(f"Error analyzing file {filename}: {e}")

        # Get datasets from database
        if self.db_engine:
            try:
                db_datasets = self._get_database_datasets()
                datasets.extend(db_datasets)
            except Exception as e:
                logger.error(f"Error getting database datasets: {e}")

        return datasets

    def _analyze_file_dataset(self, file_path: str, filename: str) -> DatasetInfo:
        """Analyze a file dataset and extract metadata"""
        try:
            # Read file based on extension
            if filename.endswith('.csv'):
                df = pd.read_csv(file_path, nrows=1000)  # Sample for analysis
            else:
                df = pd.read_excel(file_path, nrows=1000)

            # Get file size
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

            # Analyze column types
            column_types = {}
            for col in df.columns:
                if df[col].dtype in ['int64', 'float64']:
                    column_types[col] = 'numeric'
                elif df[col].dtype == 'object':
                    # Check if it's categorical or text
                    unique_ratio = df[col].nunique() / len(df)
                    if unique_ratio < 0.1:
                        column_types[col] = 'categorical'
                    else:
                        column_types[col] = 'text'
                elif df[col].dtype == 'datetime64[ns]':
                    column_types[col] = 'datetime'
                else:
                    column_types[col] = 'other'

            # Generate description based on columns and data
            description = self._generate_dataset_description(df, filename)

            return DatasetInfo(
                name=filename.split('.')[0],
                description=description,
                source_type=DataSourceType.FILE_UPLOAD,
                file_path=file_path,
                size_mb=file_size_mb,
                num_rows=len(df),
                num_columns=len(df.columns),
                column_names=list(df.columns),
                column_types=column_types
            )
        except Exception as e:
            logger.error(f"Error analyzing file {filename}: {e}")
            return DatasetInfo(
                name=filename.split('.')[0],
                description=f"Dataset from file {filename}",
                source_type=DataSourceType.FILE_UPLOAD,
                file_path=file_path
            )

    def _get_database_datasets(self) -> List[DatasetInfo]:
        """Get available datasets from database"""
        datasets = []
        try:
            # Get list of tables
            with self.db_engine.connect() as conn:
                # Get table names
                result = conn.execute(text("""
                    SELECT table_name, table_schema
                    FROM information_schema.tables
                    WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
                """))

                for row in result:
                    table_name = row[0]
                    schema = row[1]

                    try:
                        # Get table info
                        table_info = self._analyze_database_table(conn, table_name, schema)
                        datasets.append(table_info)
                    except Exception as e:
                        logger.error(f"Error analyzing table {table_name}: {e}")

        except Exception as e:
            logger.error(f"Error connecting to database: {e}")

        return datasets

    def _analyze_database_table(self, conn, table_name: str, schema: str) -> DatasetInfo:
        """Analyze a database table and extract metadata"""
        # Get column information
        columns_result = conn.execute(text(f"""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_name = '{table_name}' AND table_schema = '{schema}'
        """))

        columns = []
        column_types = {}
        for row in columns_result:
            col_name = row[0]
            data_type = row[1]
            columns.append(col_name)

            # Map database types to our types
            if data_type in ['integer', 'bigint', 'smallint', 'decimal', 'numeric', 'real', 'double precision']:
                column_types[col_name] = 'numeric'
            elif data_type in ['character varying', 'text', 'character']:
                column_types[col_name] = 'text'
            elif data_type in ['timestamp', 'date', 'time']:
                column_types[col_name] = 'datetime'
            elif data_type == 'boolean':
                column_types[col_name] = 'categorical'
            else:
                column_types[col_name] = 'other'

        # Get row count
        count_result = conn.execute(text(f"SELECT COUNT(*) FROM {schema}.{table_name}"))
        row_count = count_result.scalar()

        # Generate description
        description = f"Database table {table_name} with {len(columns)} columns and {row_count} rows"

        return DatasetInfo(
            name=f"{schema}.{table_name}",
            description=description,
            source_type=DataSourceType.DATABASE,
            table_name=f"{schema}.{table_name}",
            num_rows=row_count,
            num_columns=len(columns),
            column_names=columns,
            column_types=column_types
        )

    def _generate_dataset_description(self, df: pd.DataFrame, filename: str) -> str:
        """Generate a description for the dataset based on its content"""
        description_parts = []

        # Basic info
        description_parts.append(f"Dataset with {len(df)} rows and {len(df.columns)} columns")

        # Column types summary
        numeric_cols = len([col for col in df.columns if df[col].dtype in ['int64', 'float64']])
        text_cols = len([col for col in df.columns if df[col].dtype == 'object'])
        datetime_cols = len([col for col in df.columns if df[col].dtype == 'datetime64[ns]'])

        if numeric_cols > 0:
            description_parts.append(f"{numeric_cols} numeric columns")
        if text_cols > 0:
            description_parts.append(f"{text_cols} text/categorical columns")
        if datetime_cols > 0:
            description_parts.append(f"{datetime_cols} datetime columns")

        # Infer domain from column names
        column_names_lower = [col.lower() for col in df.columns]
        if any(word in ' '.join(column_names_lower) for word in ['price', 'cost', 'amount', 'revenue']):
            description_parts.append("Contains financial data")
        elif any(word in ' '.join(column_names_lower) for word in ['date', 'time', 'year', 'month']):
            description_parts.append("Contains temporal data")

        return ". ".join(description_parts)

    def suggest_datasets(self, user_query: str, problem_type: Optional[ProblemType] = None) -> List[DatasetInfo]:
        """Suggest datasets based on user query and problem type"""
        query_analysis = self.analyze_user_query(user_query)
        available_datasets = self.get_available_datasets()

        if not available_datasets:
            return []

        # Score datasets based on relevance
        scored_datasets = []
        for dataset in available_datasets:
            score = self._calculate_relevance_score(dataset, query_analysis, problem_type)
            scored_datasets.append((dataset, score))

        # Sort by score and return top suggestions
        scored_datasets.sort(key=lambda x: x[1], reverse=True)
        return [dataset for dataset, score in scored_datasets[:5]]

    def _calculate_relevance_score(self, dataset: DatasetInfo, query_analysis: Dict, problem_type: Optional[ProblemType]) -> float:
        """Calculate relevance score for a dataset"""
        score = 0.0

        # Base score
        score += 1.0

        # Problem type compatibility
        detected_problem_type = query_analysis.get("problem_type") or problem_type
        if detected_problem_type and dataset.column_types:
            if detected_problem_type in [ProblemType.REGRESSION, ProblemType.CLASSIFICATION]:
                # Need numeric and categorical columns
                numeric_cols = sum(1 for t in dataset.column_types.values() if t == 'numeric')
                categorical_cols = sum(1 for t in dataset.column_types.values() if t == 'categorical')
                if numeric_cols > 0:
                    score += 2.0
                if categorical_cols > 0:
                    score += 1.0
            elif detected_problem_type == ProblemType.TIME_SERIES:
                # Need datetime columns
                datetime_cols = sum(1 for t in dataset.column_types.values() if t == 'datetime')
                if datetime_cols > 0:
                    score += 3.0

        # Domain keyword matching
        domain_keywords = query_analysis.get("domain_keywords", [])
        for keyword in domain_keywords:
            if keyword.lower() in dataset.description.lower():
                score += 2.0
            if dataset.column_names:
                for col_name in dataset.column_names:
                    if keyword.lower() in col_name.lower():
                        score += 1.0

        # Query keyword matching
        query_words = query_analysis["query"].lower().split()
        for word in query_words:
            if len(word) > 3:  # Skip short words
                if word in dataset.description.lower():
                    score += 1.0
                if dataset.column_names:
                    for col_name in dataset.column_names:
                        if word in col_name.lower():
                            score += 0.5

        # Dataset size preference (medium-sized datasets are often better)
        if dataset.num_rows:
            if 1000 <= dataset.num_rows <= 100000:
                score += 1.0
            elif dataset.num_rows > 100000:
                score += 0.5

        return score


# Celery task
@task(name="suggest_datasets")
def suggest_datasets_task(user_query: str, problem_type: Optional[str] = None) -> Dict[str, Any]:
    """Celery task for dataset suggestion"""
    try:
        logger.info(f"Starting dataset suggestion for query: {user_query}")

        suggester = DatasetSuggester()
        problem_type_enum = ProblemType(problem_type) if problem_type else None

        suggestions = suggester.suggest_datasets(user_query, problem_type_enum)

        # Convert to dict for JSON serialization
        suggestions_dict = [
            {
                "name": ds.name,
                "description": ds.description,
                "source_type": ds.source_type.value,
                "file_path": ds.file_path,
                "table_name": ds.table_name,
                "num_rows": ds.num_rows,
                "num_columns": ds.num_columns,
                "column_names": ds.column_names,
                "column_types": ds.column_types,
                "size_mb": ds.size_mb
            }
            for ds in suggestions
        ]

        # Generate reasoning
        reasoning = f"Found {len(suggestions)} relevant datasets for your query '{user_query}'"
        if problem_type:
            reasoning += f" with problem type {problem_type}"

        result = {
            "suggestions": suggestions_dict,
            "reasoning": reasoning,
            "confidence_score": min(len(suggestions) / 3.0, 1.0)  # Higher confidence with more suggestions
        }

        logger.info(f"Dataset suggestion completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in dataset suggestion: {e}")
        raise