"""
COMPREHENSIVE AI CHAT WORKFLOW TESTS
Tests the complete AI chat system with mock data
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import os
from pathlib import Path
import json
from unittest.mock import Mock, patch

# Add the parent directory to the path so we can import our modules
import sys
sys.path.append(str(Path(__file__).parent.parent))

from client_agent.chatgpt_ds_assistant import ChatGPTDataScienceAssistant
from client_agent.context_alternatives_generator import ContextAlternativesGenerator
from client_agent.implementation_engine import ImplementationEngine


class TestAIChatWorkflow:
    """Test the complete AI chat workflow with mock data"""
    
    @pytest.fixture
    def mock_dataset(self):
        """Create a mock dataset for testing"""
        np.random.seed(42)
        data = {
            'zipcode': np.random.choice(['12345', '67890', '11111', '22222'], 1000),
            'price': np.random.normal(100000, 20000, 1000),
            'bedrooms': np.random.randint(1, 6, 1000),
            'bathrooms': np.random.randint(1, 4, 1000),
            'sqft': np.random.normal(2000, 500, 1000),
            'age': np.random.randint(0, 50, 1000)
        }
        
        # Add some missing values
        data['price'][np.random.choice(1000, 50, replace=False)] = np.nan
        data['sqft'][np.random.choice(1000, 30, replace=False)] = np.nan
        
        df = pd.DataFrame(data)
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        df.to_csv(temp_file.name, index=False)
        temp_file.close()
        
        return temp_file.name, df
    
    @pytest.fixture
    def mock_step_data(self, mock_dataset):
        """Create mock step data"""
        dataset_path, df = mock_dataset
        return {
            'dataset_path': dataset_path,
            'original_shape': df.shape,
            'cleaned_shape': (df.dropna().shape[0], df.shape[1]),
            'models_trained': [
                {
                    'model_name': 'Random Forest',
                    'performance_score': 0.85,
                    'training_time': 2.5,
                    'model_instance': Mock(),
                    'parameters': {'n_estimators': 100, 'max_depth': 10}
                },
                {
                    'model_name': 'XGBoost',
                    'performance_score': 0.87,
                    'training_time': 3.2,
                    'model_instance': Mock(),
                    'parameters': {'n_estimators': 200, 'learning_rate': 0.1}
                }
            ],
            'best_model': {
                'model_name': 'XGBoost',
                'performance_score': 0.87,
                'training_time': 3.2
            }
        }
    
    @pytest.fixture
    def mock_context(self):
        """Create mock context"""
        return {
            'pipeline_id': 'test_pipeline_123',
            'current_step': 'data_cleaning',
            'problem_type': 'regression',
            'user_request': 'Predict house prices based on features'
        }
    
    @pytest.fixture
    def chat_assistant(self):
        """Create ChatGPT DS Assistant instance"""
        return ChatGPTDataScienceAssistant()
    
    def test_intent_analysis_feature_engineering(self, chat_assistant, mock_context):
        """Test intent analysis for feature engineering requests"""
        
        # Test feature engineering intent
        message = "create a column by grouping zipcode and calculating average price"
        intent = chat_assistant._analyze_user_intent(message, mock_context)
        
        assert intent['requires_implementation'] == True
        assert intent['intent_type'] == 'feature_engineering'
        assert intent['confidence'] >= 0.9
        
        print("✅ Feature engineering intent detection: PASSED")
    
    def test_intent_analysis_data_cleaning(self, chat_assistant, mock_context):
        """Test intent analysis for data cleaning requests"""
        
        # Test scaling intent
        message = "apply standard scaling to numeric features"
        intent = chat_assistant._analyze_user_intent(message, mock_context)
        
        assert intent['requires_implementation'] == True
        assert intent['intent_type'] == 'data_cleaning'
        assert intent['confidence'] >= 0.8
        
        # Test missing value intent
        message = "fill missing values with median"
        intent = chat_assistant._analyze_user_intent(message, mock_context)
        
        assert intent['requires_implementation'] == True
        assert intent['intent_type'] == 'data_cleaning'
        
        print("✅ Data cleaning intent detection: PASSED")
    
    def test_intent_analysis_information_request(self, chat_assistant, mock_context):
        """Test intent analysis for information requests"""
        
        message = "what parameters were used for the models?"
        intent = chat_assistant._analyze_user_intent(message, mock_context)
        
        assert intent['requires_implementation'] == False
        assert intent['intent_type'] == 'information_request'
        
        print("✅ Information request intent detection: PASSED")
    
    def test_alternatives_generation_data_cleaning(self, chat_assistant, mock_step_data, mock_context):
        """Test alternatives generation for data cleaning step"""
        
        alternatives = chat_assistant.generate_rejection_alternatives(
            'data_cleaning', mock_step_data, mock_context
        )
        
        assert len(alternatives) > 0
        assert any(alt['action_type'] == 'missing_value_handling' for alt in alternatives)
        assert any(alt['action_type'] == 'scaling' for alt in alternatives)
        assert any(alt['action_type'] == 'outlier_handling' for alt in alternatives)
        
        # Check structure of alternatives
        for alt in alternatives:
            assert 'id' in alt
            assert 'title' in alt
            assert 'description' in alt
            assert 'implementation' in alt
            assert 'type' in alt['implementation']
            assert 'operation' in alt['implementation']
        
        print(f"✅ Data cleaning alternatives generation: PASSED ({len(alternatives)} alternatives)")
    
    def test_alternatives_generation_feature_engineering(self, chat_assistant, mock_step_data, mock_context):
        """Test alternatives generation for feature engineering step"""
        
        alternatives = chat_assistant.generate_rejection_alternatives(
            'feature_engineering', mock_step_data, mock_context
        )
        
        assert len(alternatives) > 0
        assert any(alt['action_type'] == 'feature_creation' for alt in alternatives)
        assert any(alt['action_type'] == 'feature_selection' for alt in alternatives)
        
        print(f"✅ Feature engineering alternatives generation: PASSED ({len(alternatives)} alternatives)")
    
    def test_implementation_engine_data_grouping(self, mock_step_data):
        """Test implementation engine for data grouping"""
        
        engine = ImplementationEngine()
        
        # Test data grouping implementation
        implementation = {
            'type': 'feature_engineering',
            'operation': 'group_aggregate',
            'config': {
                'group_column': 'zipcode',
                'target_column': 'price',
                'aggregation': 'mean',
                'new_column_name': 'zipcode_avg_price'
            }
        }
        
        result = engine._execute_feature_engineering(
            implementation, {}, mock_step_data, 'test_pipeline'
        )
        
        assert result['success'] == True
        assert 'zipcode_avg_price' in result['description']
        assert 'new_dataset_path' in result
        
        # Verify the new dataset was created
        new_df = pd.read_csv(result['new_dataset_path'])
        assert 'zipcode_avg_price' in new_df.columns
        assert new_df.shape[1] > pd.read_csv(mock_step_data['dataset_path']).shape[1]
        
        print("✅ Data grouping implementation: PASSED")
    
    def test_implementation_engine_scaling(self, mock_step_data):
        """Test implementation engine for scaling"""
        
        engine = ImplementationEngine()
        
        # Test scaling implementation
        implementation = {
            'type': 'data_cleaning',
            'operation': 'scale_features',
            'config': {
                'scaler': 'StandardScaler'
            }
        }
        
        result = engine._execute_data_cleaning(
            implementation, {}, mock_step_data, 'test_pipeline'
        )
        
        assert result['success'] == True
        assert 'StandardScaler' in result['description']
        assert 'new_dataset_path' in result
        
        print("✅ Scaling implementation: PASSED")
    
    def test_implementation_engine_missing_values(self, mock_step_data):
        """Test implementation engine for missing value handling"""
        
        engine = ImplementationEngine()
        
        # Test missing value imputation
        implementation = {
            'type': 'data_cleaning',
            'operation': 'impute_missing',
            'config': {
                'strategy': 'median'
            }
        }
        
        result = engine._execute_data_cleaning(
            implementation, {}, mock_step_data, 'test_pipeline'
        )
        
        assert result['success'] == True
        assert 'median' in result['description']
        
        # Verify missing values were handled
        new_df = pd.read_csv(result['new_dataset_path'])
        original_df = pd.read_csv(mock_step_data['dataset_path'])
        
        assert new_df.isnull().sum().sum() <= original_df.isnull().sum().sum()
        
        print("✅ Missing value handling implementation: PASSED")
    
    def test_user_request_parsing(self):
        """Test user request parsing from natural language"""
        
        engine = ImplementationEngine()
        
        # Test feature engineering parsing
        message = "create a column by grouping zipcode and calculating average price"
        intent_data = engine._parse_user_intent(message, {})
        
        assert intent_data['success'] == True
        assert intent_data['intent_type'] == 'feature_engineering'
        assert intent_data['operation'] == 'group_aggregate'
        assert intent_data['entities']['group_column'] == 'zipcode'
        assert intent_data['entities']['aggregation'] in ['average', 'mean', 'avg']
        assert intent_data['entities']['target_column'] == 'price'
        
        # Test scaling parsing
        message = "apply standard scaling to features"
        intent_data = engine._parse_user_intent(message, {})
        
        assert intent_data['success'] == True
        assert intent_data['intent_type'] == 'data_cleaning'
        assert intent_data['operation'] == 'scale_features'
        assert intent_data['entities']['scaler_type'] == 'standard'
        
        print("✅ User request parsing: PASSED")
    
    def test_complete_chat_workflow_implementation(self, chat_assistant, mock_step_data, mock_context):
        """Test complete chat workflow for implementation requests"""
        
        # Test feature engineering request
        message = "create a column by grouping zipcode and calculating average price"
        
        # Update context with step data
        context = mock_context.copy()
        context['step_data'] = mock_step_data
        
        response = chat_assistant.chat(message, 'test_pipeline', context)
        
        assert 'response' in response
        assert response.get('implementation_completed', False) == True
        assert 'implementation_details' in response
        assert response['implementation_details']['success'] == True
        
        print("✅ Complete implementation workflow: PASSED")
    
    def test_complete_chat_workflow_information(self, chat_assistant, mock_step_data, mock_context):
        """Test complete chat workflow for information requests"""
        
        message = "what models were trained and their performance?"
        
        # Update context with step data
        context = mock_context.copy()
        context['step_data'] = mock_step_data
        
        response = chat_assistant.chat(message, 'test_pipeline', context)
        
        assert 'response' in response
        assert response.get('implementation_completed', False) == False
        assert 'suggestions' in response
        
        print("✅ Complete information workflow: PASSED")
    
    def test_conversation_memory(self, chat_assistant):
        """Test conversation memory functionality"""
        
        pipeline_id = 'test_pipeline_memory'
        
        # First message
        chat_assistant._store_conversation(pipeline_id, "Hello", "Hi there!")
        
        # Second message
        chat_assistant._store_conversation(pipeline_id, "Create a feature", "I'll create a feature for you")
        
        # Check memory
        assert pipeline_id in chat_assistant.conversation_memory
        assert len(chat_assistant.conversation_memory[pipeline_id]) == 2
        
        print("✅ Conversation memory: PASSED")
    
    def teardown_method(self, method):
        """Clean up temporary files after each test"""
        # Clean up any temporary CSV files
        temp_dir = tempfile.gettempdir()
        for file in Path(temp_dir).glob("*.csv"):
            try:
                if file.stat().st_mtime > 0:  # Recent files
                    file.unlink()
            except:
                pass


def run_all_tests():
    """Run all tests manually"""
    print("🧪 Starting AI Chat Workflow Tests...\n")
    
    test_instance = TestAIChatWorkflow()
    
    # Create fixtures
    mock_dataset = test_instance.mock_dataset()
    mock_step_data = test_instance.mock_step_data(mock_dataset)
    mock_context = test_instance.mock_context()
    chat_assistant = test_instance.chat_assistant()
    
    try:
        # Run all tests
        test_instance.test_intent_analysis_feature_engineering(chat_assistant, mock_context)
        test_instance.test_intent_analysis_data_cleaning(chat_assistant, mock_context)
        test_instance.test_intent_analysis_information_request(chat_assistant, mock_context)
        test_instance.test_alternatives_generation_data_cleaning(chat_assistant, mock_step_data, mock_context)
        test_instance.test_alternatives_generation_feature_engineering(chat_assistant, mock_step_data, mock_context)
        test_instance.test_implementation_engine_data_grouping(mock_step_data)
        test_instance.test_implementation_engine_scaling(mock_step_data)
        test_instance.test_implementation_engine_missing_values(mock_step_data)
        test_instance.test_user_request_parsing()
        test_instance.test_complete_chat_workflow_implementation(chat_assistant, mock_step_data, mock_context)
        test_instance.test_complete_chat_workflow_information(chat_assistant, mock_step_data, mock_context)
        test_instance.test_conversation_memory(chat_assistant)
        
        print("\n🎉 ALL TESTS PASSED! AI Chat Workflow is working correctly.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        raise
    
    finally:
        # Cleanup
        test_instance.teardown_method(None)


if __name__ == "__main__":
    run_all_tests()
