"""
DEBUG AI CHAT TEST
Simple debug test to understand what's happening
"""

import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from client_agent.chatgpt_ds_assistant import ChatGPTDataScienceAssistant
    print("✅ Successfully imported ChatGPTDataScienceAssistant")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


def debug_intent_analysis():
    """Debug intent analysis step by step"""
    print("🔍 DEBUGGING INTENT ANALYSIS")
    print("=" * 40)
    
    chat_assistant = ChatGPTDataScienceAssistant()
    context = {
        'pipeline_id': 'test_pipeline_123',
        'current_step': 'data_cleaning',
        'problem_type': 'regression'
    }
    
    # Test messages
    test_messages = [
        "create a column by grouping zipcode and calculating average price",
        "create a new column",
        "add a feature",
        "apply standard scaling",
        "what parameters were used?",
        "explain the results",
        "set learning_rate to 0.01",
        "yes proceed",
        "implement this"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}. Testing: '{message}'")
        print("-" * 50)
        
        try:
            intent = chat_assistant._analyze_user_intent(message, context)
            
            print(f"   Intent Type: {intent.get('intent_type', 'Unknown')}")
            print(f"   Requires Implementation: {intent.get('requires_implementation', False)}")
            print(f"   Confidence: {intent.get('confidence', 0)}")
            print(f"   Pattern Matched: {intent.get('pattern_matched', 'None')}")
            
            if intent.get('requires_implementation'):
                print("   ✅ IMPLEMENTATION REQUEST")
            else:
                print("   ℹ️ INFORMATION REQUEST")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")


def debug_implementation_engine():
    """Debug implementation engine"""
    print("\n🔧 DEBUGGING IMPLEMENTATION ENGINE")
    print("=" * 40)
    
    try:
        from client_agent.implementation_engine import ImplementationEngine
        
        engine = ImplementationEngine()
        
        # Test parsing
        test_messages = [
            "create a column by grouping zipcode and calculating average price",
            "apply standard scaling to features",
            "fill missing values with median",
            "set learning_rate to 0.01"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}. Parsing: '{message}'")
            print("-" * 50)
            
            try:
                intent_data = engine._parse_user_intent(message, {})
                
                print(f"   Success: {intent_data.get('success', False)}")
                print(f"   Intent Type: {intent_data.get('intent_type', 'Unknown')}")
                print(f"   Operation: {intent_data.get('operation', 'Unknown')}")
                print(f"   Entities: {intent_data.get('entities', {})}")
                
                if intent_data.get('success'):
                    print("   ✅ PARSING SUCCESSFUL")
                else:
                    print(f"   ❌ PARSING FAILED: {intent_data.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ ERROR: {e}")
                
    except ImportError as e:
        print(f"❌ Could not import ImplementationEngine: {e}")


def debug_alternatives_generation():
    """Debug alternatives generation"""
    print("\n🔄 DEBUGGING ALTERNATIVES GENERATION")
    print("=" * 40)
    
    try:
        from client_agent.context_alternatives_generator import ContextAlternativesGenerator
        
        generator = ContextAlternativesGenerator()
        
        # Mock step data
        step_data = {
            'dataset_path': 'mock_dataset.csv',
            'original_shape': (1000, 5),
            'models_trained': []
        }
        
        context = {
            'problem_type': 'regression'
        }
        
        # Test different step types
        step_types = ['data_cleaning', 'feature_engineering', 'model_training']
        
        for step_type in step_types:
            print(f"\n📋 Testing {step_type} alternatives:")
            print("-" * 30)
            
            try:
                alternatives = generator.generate_alternatives(step_type, step_data, context)
                
                print(f"   Generated: {len(alternatives)} alternatives")
                
                for j, alt in enumerate(alternatives[:3], 1):  # Show first 3
                    print(f"   {j}. {alt.get('title', 'Unknown')}")
                    print(f"      Type: {alt.get('action_type', 'Unknown')}")
                    print(f"      Description: {alt.get('description', 'No description')[:60]}...")
                
                if alternatives:
                    print("   ✅ ALTERNATIVES GENERATED")
                else:
                    print("   ⚠️ NO ALTERNATIVES GENERATED")
                    
            except Exception as e:
                print(f"   ❌ ERROR: {e}")
                
    except ImportError as e:
        print(f"❌ Could not import ContextAlternativesGenerator: {e}")


def debug_complete_workflow():
    """Debug complete workflow"""
    print("\n💬 DEBUGGING COMPLETE WORKFLOW")
    print("=" * 40)
    
    chat_assistant = ChatGPTDataScienceAssistant()
    
    # Mock context with step data
    context = {
        'pipeline_id': 'test_pipeline_123',
        'current_step': 'data_cleaning',
        'problem_type': 'regression',
        'step_data': {
            'dataset_path': 'mock_dataset.csv',
            'original_shape': (1000, 5),
            'models_trained': [
                {
                    'model_name': 'Random Forest',
                    'performance_score': 0.85,
                    'parameters': {'n_estimators': 100}
                }
            ]
        }
    }
    
    # Test messages
    test_messages = [
        "what models were trained?",
        "create a new feature column",
        "apply scaling to the data"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}. Complete workflow test: '{message}'")
        print("-" * 50)
        
        try:
            response = chat_assistant.chat(message, 'test_pipeline', context)
            
            print(f"   Response Length: {len(response.get('response', ''))}")
            print(f"   Implementation Completed: {response.get('implementation_completed', False)}")
            print(f"   Show Input Field: {response.get('show_input_field', False)}")
            print(f"   Suggestions Count: {len(response.get('suggestions', []))}")
            
            # Show first 100 chars of response
            response_preview = response.get('response', '')[:100]
            print(f"   Response Preview: {response_preview}...")
            
            print("   ✅ WORKFLOW COMPLETED")
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            import traceback
            traceback.print_exc()


def main():
    """Run all debug tests"""
    print("🐛 AI CHAT DEBUG TEST SUITE")
    print("=" * 50)
    
    try:
        debug_intent_analysis()
        debug_implementation_engine()
        debug_alternatives_generation()
        debug_complete_workflow()
        
        print("\n" + "=" * 50)
        print("🎉 DEBUG TESTS COMPLETED!")
        print("Check the output above to identify any issues")
        
    except Exception as e:
        print(f"\n❌ DEBUG TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
