"""
CONTEXT-AWARE ALTERNATIVES GENERATOR
Generates specific alternatives based on pipeline step and data context
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from loguru import logger


class ContextAlternativesGenerator:
    """Generates context-specific alternatives for each pipeline step"""
    
    def __init__(self):
        self.step_alternatives = {
            'data_cleaning': self._generate_data_cleaning_alternatives,
            'feature_engineering': self._generate_feature_engineering_alternatives,
            'model_training': self._generate_model_training_alternatives,
            'hyperparameter_tuning': self._generate_hyperparameter_alternatives,
            'model_evaluation': self._generate_evaluation_alternatives
        }
    
    def generate_alternatives(self, step_name: str, step_data: Dict[str, Any], 
                            context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Generate context-specific alternatives for a rejected step"""
        
        step_type = self._identify_step_type(step_name)
        
        if step_type in self.step_alternatives:
            return self.step_alternatives[step_type](step_data, context or {})
        else:
            return self._generate_generic_alternatives(step_name, step_data)
    
    def _identify_step_type(self, step_name: str) -> str:
        """Identify the type of step based on name"""
        step_lower = step_name.lower()
        
        if any(keyword in step_lower for keyword in ['clean', 'preprocess', 'missing']):
            return 'data_cleaning'
        elif any(keyword in step_lower for keyword in ['feature', 'engineer', 'transform']):
            return 'feature_engineering'
        elif any(keyword in step_lower for keyword in ['train', 'model', 'algorithm']):
            return 'model_training'
        elif any(keyword in step_lower for keyword in ['hyperparameter', 'tuning', 'optimize']):
            return 'hyperparameter_tuning'
        elif any(keyword in step_lower for keyword in ['evaluat', 'metric', 'performance']):
            return 'model_evaluation'
        else:
            return 'generic'
    
    def _generate_data_cleaning_alternatives(self, step_data: Dict[str, Any], 
                                           context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate data cleaning alternatives"""
        
        alternatives = []
        
        # Get data info
        dataset_info = self._analyze_dataset(step_data, context)
        
        # Missing value handling alternatives
        if dataset_info.get('has_missing_values', False):
            alternatives.extend([
                {
                    "id": "missing_drop",
                    "title": "Drop Missing Values",
                    "description": "Remove rows with missing values",
                    "action_type": "missing_value_handling",
                    "parameters": {"method": "drop", "threshold": 0.5},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "drop_missing",
                        "config": {"axis": 0, "how": "any"}
                    }
                },
                {
                    "id": "missing_impute_mean",
                    "title": "Mean Imputation",
                    "description": "Fill missing values with column mean",
                    "action_type": "missing_value_handling",
                    "parameters": {"method": "mean"},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "impute_missing",
                        "config": {"strategy": "mean", "numeric_only": True}
                    }
                },
                {
                    "id": "missing_impute_median",
                    "title": "Median Imputation",
                    "description": "Fill missing values with column median",
                    "action_type": "missing_value_handling",
                    "parameters": {"method": "median"},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "impute_missing",
                        "config": {"strategy": "median", "numeric_only": True}
                    }
                },
                {
                    "id": "missing_forward_fill",
                    "title": "Forward Fill",
                    "description": "Fill missing values with previous value",
                    "action_type": "missing_value_handling",
                    "parameters": {"method": "ffill"},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "forward_fill",
                        "config": {"method": "ffill"}
                    }
                }
            ])
        
        # Outlier handling alternatives
        if dataset_info.get('has_outliers', False):
            alternatives.extend([
                {
                    "id": "outlier_remove_iqr",
                    "title": "Remove Outliers (IQR Method)",
                    "description": "Remove outliers using Interquartile Range method",
                    "action_type": "outlier_handling",
                    "parameters": {"method": "iqr", "factor": 1.5},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "remove_outliers",
                        "config": {"method": "iqr", "factor": 1.5}
                    }
                },
                {
                    "id": "outlier_remove_zscore",
                    "title": "Remove Outliers (Z-Score)",
                    "description": "Remove outliers using Z-Score method (threshold=3)",
                    "action_type": "outlier_handling",
                    "parameters": {"method": "zscore", "threshold": 3},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "remove_outliers",
                        "config": {"method": "zscore", "threshold": 3}
                    }
                },
                {
                    "id": "outlier_cap",
                    "title": "Cap Outliers",
                    "description": "Cap outliers at 95th and 5th percentiles",
                    "action_type": "outlier_handling",
                    "parameters": {"method": "cap", "lower": 0.05, "upper": 0.95},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "cap_outliers",
                        "config": {"lower_percentile": 0.05, "upper_percentile": 0.95}
                    }
                }
            ])
        
        # Scaling alternatives
        alternatives.extend([
            {
                "id": "scaling_standard",
                "title": "Standard Scaling",
                "description": "Standardize features (mean=0, std=1)",
                "action_type": "scaling",
                "parameters": {"method": "standard"},
                "implementation": {
                    "type": "data_cleaning",
                    "operation": "scale_features",
                    "config": {"scaler": "StandardScaler"}
                }
            },
            {
                "id": "scaling_minmax",
                "title": "Min-Max Scaling",
                "description": "Scale features to range [0, 1]",
                "action_type": "scaling",
                "parameters": {"method": "minmax"},
                "implementation": {
                    "type": "data_cleaning",
                    "operation": "scale_features",
                    "config": {"scaler": "MinMaxScaler"}
                }
            },
            {
                "id": "scaling_robust",
                "title": "Robust Scaling",
                "description": "Scale using median and IQR (robust to outliers)",
                "action_type": "scaling",
                "parameters": {"method": "robust"},
                "implementation": {
                    "type": "data_cleaning",
                    "operation": "scale_features",
                    "config": {"scaler": "RobustScaler"}
                }
            }
        ])
        
        # Imbalanced data handling (if classification)
        if dataset_info.get('is_classification', False) and dataset_info.get('is_imbalanced', False):
            alternatives.extend([
                {
                    "id": "balance_smote",
                    "title": "SMOTE Oversampling",
                    "description": "Generate synthetic samples for minority class",
                    "action_type": "imbalance_handling",
                    "parameters": {"method": "smote"},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "balance_data",
                        "config": {"method": "SMOTE", "random_state": 42}
                    }
                },
                {
                    "id": "balance_undersample",
                    "title": "Random Undersampling",
                    "description": "Randomly remove samples from majority class",
                    "action_type": "imbalance_handling",
                    "parameters": {"method": "undersample"},
                    "implementation": {
                        "type": "data_cleaning",
                        "operation": "balance_data",
                        "config": {"method": "RandomUnderSampler", "random_state": 42}
                    }
                }
            ])
        
        return alternatives[:6]  # Return top 6 alternatives
    
    def _generate_feature_engineering_alternatives(self, step_data: Dict[str, Any], 
                                                 context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate feature engineering alternatives"""
        
        alternatives = []
        dataset_info = self._analyze_dataset(step_data, context)
        
        # Polynomial features
        alternatives.append({
            "id": "poly_features",
            "title": "Polynomial Features",
            "description": "Create polynomial and interaction features",
            "action_type": "feature_creation",
            "parameters": {"degree": 2, "interaction_only": False},
            "implementation": {
                "type": "feature_engineering",
                "operation": "polynomial_features",
                "config": {"degree": 2, "include_bias": False}
            }
        })
        
        # Log transformation
        if dataset_info.get('has_numeric_features', False):
            alternatives.append({
                "id": "log_transform",
                "title": "Log Transformation",
                "description": "Apply log transformation to skewed features",
                "action_type": "feature_transformation",
                "parameters": {"method": "log"},
                "implementation": {
                    "type": "feature_engineering",
                    "operation": "log_transform",
                    "config": {"add_constant": 1}
                }
            })
        
        # Binning/Discretization
        alternatives.append({
            "id": "feature_binning",
            "title": "Feature Binning",
            "description": "Convert continuous features to categorical bins",
            "action_type": "feature_transformation",
            "parameters": {"method": "equal_width", "bins": 5},
            "implementation": {
                "type": "feature_engineering",
                "operation": "bin_features",
                "config": {"strategy": "quantile", "n_bins": 5}
            }
        })
        
        # Feature selection
        alternatives.extend([
            {
                "id": "select_kbest",
                "title": "Select K-Best Features",
                "description": "Select top K features based on statistical tests",
                "action_type": "feature_selection",
                "parameters": {"k": 10, "score_func": "f_classif"},
                "implementation": {
                    "type": "feature_engineering",
                    "operation": "select_features",
                    "config": {"method": "SelectKBest", "k": 10}
                }
            },
            {
                "id": "rfe_selection",
                "title": "Recursive Feature Elimination",
                "description": "Select features using recursive elimination",
                "action_type": "feature_selection",
                "parameters": {"n_features": 10},
                "implementation": {
                    "type": "feature_engineering",
                    "operation": "select_features",
                    "config": {"method": "RFE", "n_features_to_select": 10}
                }
            }
        ])
        
        return alternatives[:5]  # Return top 5 alternatives
    
    def _generate_model_training_alternatives(self, step_data: Dict[str, Any], 
                                            context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate model training alternatives"""
        
        alternatives = []
        dataset_info = self._analyze_dataset(step_data, context)
        
        # Different algorithms based on problem type
        if dataset_info.get('is_classification', False):
            alternatives.extend([
                {
                    "id": "random_forest_clf",
                    "title": "Random Forest Classifier",
                    "description": "Ensemble method with multiple decision trees",
                    "action_type": "algorithm_change",
                    "parameters": {"n_estimators": 100, "max_depth": None},
                    "implementation": {
                        "type": "model_training",
                        "operation": "train_model",
                        "config": {"algorithm": "RandomForestClassifier", "params": {"n_estimators": 100}}
                    }
                },
                {
                    "id": "xgboost_clf",
                    "title": "XGBoost Classifier",
                    "description": "Gradient boosting with advanced optimization",
                    "action_type": "algorithm_change",
                    "parameters": {"n_estimators": 100, "learning_rate": 0.1},
                    "implementation": {
                        "type": "model_training",
                        "operation": "train_model",
                        "config": {"algorithm": "XGBClassifier", "params": {"n_estimators": 100, "learning_rate": 0.1}}
                    }
                },
                {
                    "id": "svm_clf",
                    "title": "Support Vector Machine",
                    "description": "SVM with RBF kernel for non-linear classification",
                    "action_type": "algorithm_change",
                    "parameters": {"kernel": "rbf", "C": 1.0},
                    "implementation": {
                        "type": "model_training",
                        "operation": "train_model",
                        "config": {"algorithm": "SVC", "params": {"kernel": "rbf", "C": 1.0}}
                    }
                }
            ])
        else:
            # Regression alternatives
            alternatives.extend([
                {
                    "id": "random_forest_reg",
                    "title": "Random Forest Regressor",
                    "description": "Ensemble method for regression tasks",
                    "action_type": "algorithm_change",
                    "parameters": {"n_estimators": 100, "max_depth": None},
                    "implementation": {
                        "type": "model_training",
                        "operation": "train_model",
                        "config": {"algorithm": "RandomForestRegressor", "params": {"n_estimators": 100}}
                    }
                },
                {
                    "id": "xgboost_reg",
                    "title": "XGBoost Regressor",
                    "description": "Gradient boosting for regression",
                    "action_type": "algorithm_change",
                    "parameters": {"n_estimators": 100, "learning_rate": 0.1},
                    "implementation": {
                        "type": "model_training",
                        "operation": "train_model",
                        "config": {"algorithm": "XGBRegressor", "params": {"n_estimators": 100, "learning_rate": 0.1}}
                    }
                }
            ])
        
        return alternatives[:4]  # Return top 4 alternatives

    def _generate_hyperparameter_alternatives(self, step_data: Dict[str, Any],
                                            context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate hyperparameter tuning alternatives"""

        alternatives = [
            {
                "id": "grid_search",
                "title": "Grid Search CV",
                "description": "Exhaustive search over parameter grid",
                "action_type": "hyperparameter_tuning",
                "parameters": {"cv": 5, "scoring": "accuracy"},
                "implementation": {
                    "type": "hyperparameter_tuning",
                    "operation": "grid_search",
                    "config": {"cv": 5, "n_jobs": -1}
                }
            },
            {
                "id": "random_search",
                "title": "Random Search CV",
                "description": "Random sampling of parameter space",
                "action_type": "hyperparameter_tuning",
                "parameters": {"n_iter": 100, "cv": 5},
                "implementation": {
                    "type": "hyperparameter_tuning",
                    "operation": "random_search",
                    "config": {"n_iter": 100, "cv": 5, "n_jobs": -1}
                }
            },
            {
                "id": "bayesian_optimization",
                "title": "Bayesian Optimization",
                "description": "Smart parameter search using Bayesian methods",
                "action_type": "hyperparameter_tuning",
                "parameters": {"n_calls": 50},
                "implementation": {
                    "type": "hyperparameter_tuning",
                    "operation": "bayesian_search",
                    "config": {"n_calls": 50, "random_state": 42}
                }
            }
        ]

        return alternatives

    def _generate_evaluation_alternatives(self, step_data: Dict[str, Any],
                                        context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate model evaluation alternatives"""

        dataset_info = self._analyze_dataset(step_data, context)
        alternatives = []

        if dataset_info.get('is_classification', False):
            alternatives.extend([
                {
                    "id": "classification_report",
                    "title": "Detailed Classification Report",
                    "description": "Precision, recall, F1-score for each class",
                    "action_type": "evaluation_metric",
                    "parameters": {"include_support": True},
                    "implementation": {
                        "type": "model_evaluation",
                        "operation": "classification_report",
                        "config": {"output_dict": True}
                    }
                },
                {
                    "id": "confusion_matrix",
                    "title": "Confusion Matrix Analysis",
                    "description": "Detailed confusion matrix with visualization",
                    "action_type": "evaluation_metric",
                    "parameters": {"normalize": "true"},
                    "implementation": {
                        "type": "model_evaluation",
                        "operation": "confusion_matrix",
                        "config": {"normalize": "true", "plot": True}
                    }
                },
                {
                    "id": "roc_analysis",
                    "title": "ROC Curve Analysis",
                    "description": "ROC curves and AUC scores",
                    "action_type": "evaluation_metric",
                    "parameters": {"multi_class": "ovr"},
                    "implementation": {
                        "type": "model_evaluation",
                        "operation": "roc_analysis",
                        "config": {"multi_class": "ovr", "plot": True}
                    }
                }
            ])
        else:
            alternatives.extend([
                {
                    "id": "regression_metrics",
                    "title": "Comprehensive Regression Metrics",
                    "description": "MAE, MSE, RMSE, R², adjusted R²",
                    "action_type": "evaluation_metric",
                    "parameters": {"include_adjusted_r2": True},
                    "implementation": {
                        "type": "model_evaluation",
                        "operation": "regression_metrics",
                        "config": {"metrics": ["mae", "mse", "rmse", "r2"]}
                    }
                },
                {
                    "id": "residual_analysis",
                    "title": "Residual Analysis",
                    "description": "Residual plots and distribution analysis",
                    "action_type": "evaluation_metric",
                    "parameters": {"plot_residuals": True},
                    "implementation": {
                        "type": "model_evaluation",
                        "operation": "residual_analysis",
                        "config": {"plot": True, "qq_plot": True}
                    }
                }
            ])

        return alternatives

    def _generate_generic_alternatives(self, step_name: str, step_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate generic alternatives for unknown step types"""

        return [
            {
                "id": "retry_step",
                "title": "Retry with Different Parameters",
                "description": f"Retry {step_name} with modified parameters",
                "action_type": "retry",
                "parameters": {"modified": True},
                "implementation": {
                    "type": "generic",
                    "operation": "retry_step",
                    "config": {"step_name": step_name}
                }
            },
            {
                "id": "skip_step",
                "title": "Skip This Step",
                "description": f"Skip {step_name} and proceed to next step",
                "action_type": "skip",
                "parameters": {"skip": True},
                "implementation": {
                    "type": "generic",
                    "operation": "skip_step",
                    "config": {"step_name": step_name}
                }
            }
        ]

    def _analyze_dataset(self, step_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze dataset to determine appropriate alternatives"""

        analysis = {
            'has_missing_values': False,
            'has_outliers': False,
            'has_numeric_features': False,
            'is_classification': False,
            'is_imbalanced': False,
            'feature_count': 0,
            'sample_count': 0
        }

        try:
            # Try to get dataset info from step_data or context
            if 'dataset_path' in step_data:
                # If we have dataset path, we could analyze it
                # For now, make reasonable assumptions
                analysis.update({
                    'has_missing_values': True,  # Assume there might be missing values
                    'has_outliers': True,       # Assume there might be outliers
                    'has_numeric_features': True, # Assume numeric features exist
                    'feature_count': step_data.get('feature_count', 10),
                    'sample_count': step_data.get('sample_count', 1000)
                })

            # Determine problem type from context
            problem_type = context.get('problem_type') or step_data.get('problem_type', 'classification')
            analysis['is_classification'] = problem_type.lower() in ['classification', 'binary', 'multiclass']

            # Check for class imbalance (assume imbalanced for demo)
            if analysis['is_classification']:
                analysis['is_imbalanced'] = True  # Conservative assumption

        except Exception as e:
            logger.warning(f"Dataset analysis failed: {e}")

        return analysis
