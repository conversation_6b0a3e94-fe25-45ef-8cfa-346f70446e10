"""
Data insights generation tool for the AI Data Science Pipeline
"""
import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger
import warnings
warnings.filterwarnings('ignore')

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings
from mcp_server.models import DataInsights
from mcp_server.celery import task


class InsightsGenerator:
    """Advanced data insights generation service"""

    def __init__(self):
        self.charts_dir = os.path.join(settings.UPLOAD_DIR, "charts")
        os.makedirs(self.charts_dir, exist_ok=True)

    def generate_insights(self, dataset_path: str, target_column: Optional[str] = None) -> Dict[str, Any]:
        """Generate comprehensive data insights"""
        logger.info(f"Generating insights for dataset: {dataset_path}")

        # Load dataset
        df = self._load_dataset(dataset_path)

        # Basic statistics
        basic_stats = self._calculate_basic_stats(df)

        # Missing values analysis
        missing_values = self._analyze_missing_values(df)

        # Data types analysis
        data_types = self._analyze_data_types(df)

        # Unique values analysis
        unique_values = self._analyze_unique_values(df)

        # Outliers detection
        outliers = self._detect_outliers(df)

        # Correlation analysis
        correlation_matrix = self._calculate_correlation_matrix(df)

        # Generate visualizations
        visualizations = self._generate_visualizations(df, target_column)

        # Generate insights summary
        insights_summary = self._generate_insights_summary(
            df, basic_stats, missing_values, outliers, target_column
        )

        result = {
            "basic_stats": basic_stats,
            "correlation_matrix": correlation_matrix,
            "missing_values": missing_values,
            "data_types": data_types,
            "unique_values": unique_values,
            "outliers": outliers,
            "visualizations": visualizations,
            "insights_summary": insights_summary
        }

        logger.info("Data insights generation completed")
        return result

    def _load_dataset(self, dataset_path: str) -> pd.DataFrame:
        """Load dataset from file"""
        try:
            if dataset_path.endswith('.csv'):
                df = pd.read_csv(dataset_path)
            elif dataset_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(dataset_path)
            elif dataset_path.endswith('.json'):
                df = pd.read_json(dataset_path)
            else:
                raise ValueError(f"Unsupported file format: {dataset_path}")

            logger.info(f"Loaded dataset with shape: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise

    def _calculate_basic_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate basic statistical measures"""
        stats = {}

        # Numeric columns statistics
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            numeric_stats = df[numeric_cols].describe()
            stats['numeric'] = numeric_stats.to_dict()

        # Categorical columns statistics
        categorical_cols = df.select_dtypes(include=['object']).columns
        if len(categorical_cols) > 0:
            categorical_stats = {}
            for col in categorical_cols:
                categorical_stats[col] = {
                    'count': df[col].count(),
                    'unique': df[col].nunique(),
                    'top': df[col].mode().iloc[0] if len(df[col].mode()) > 0 else None,
                    'freq': df[col].value_counts().iloc[0] if len(df[col]) > 0 else 0
                }
            stats['categorical'] = categorical_stats

        # Overall dataset statistics
        stats['overall'] = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'numeric_columns': len(numeric_cols),
            'categorical_columns': len(categorical_cols),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024
        }

        return stats

    def _analyze_missing_values(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze missing values in the dataset"""
        missing_values = {}

        for col in df.columns:
            missing_count = df[col].isnull().sum()
            missing_values[col] = int(missing_count)

        return missing_values

    def _analyze_data_types(self, df: pd.DataFrame) -> Dict[str, str]:
        """Analyze data types of columns"""
        data_types = {}

        for col in df.columns:
            dtype = str(df[col].dtype)

            # Simplify dtype names
            if 'int' in dtype:
                data_types[col] = 'integer'
            elif 'float' in dtype:
                data_types[col] = 'float'
            elif 'object' in dtype:
                data_types[col] = 'text'
            elif 'datetime' in dtype:
                data_types[col] = 'datetime'
            elif 'bool' in dtype:
                data_types[col] = 'boolean'
            else:
                data_types[col] = dtype

        return data_types

    def _analyze_unique_values(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze unique values in each column"""
        unique_values = {}

        for col in df.columns:
            unique_values[col] = int(df[col].nunique())

        return unique_values

    def _detect_outliers(self, df: pd.DataFrame) -> Dict[str, int]:
        """Detect outliers using IQR method"""
        outliers = {}

        numeric_cols = df.select_dtypes(include=[np.number]).columns

        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1

            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            outlier_count = len(df[(df[col] < lower_bound) | (df[col] > upper_bound)])
            outliers[col] = outlier_count

        return outliers

    def _calculate_correlation_matrix(self, df: pd.DataFrame) -> Optional[Dict[str, Dict[str, float]]]:
        """Calculate correlation matrix for numeric columns"""
        try:
            numeric_cols = df.select_dtypes(include=[np.number]).columns

            if len(numeric_cols) < 2:
                return None

            corr_matrix = df[numeric_cols].corr()

            # Convert to nested dictionary
            correlation_dict = {}
            for col1 in corr_matrix.columns:
                correlation_dict[col1] = {}
                for col2 in corr_matrix.columns:
                    correlation_dict[col1][col2] = float(corr_matrix.loc[col1, col2])

            return correlation_dict
        except Exception as e:
            logger.warning(f"Error calculating correlation matrix: {e}")
            return None

    def _generate_visualizations(self, df: pd.DataFrame, target_column: Optional[str] = None) -> Dict[str, str]:
        """Generate visualization charts"""
        visualizations = {}

        try:
            # Distribution plots for numeric columns
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                dist_path = self._plot_distributions(df, numeric_cols)
                if dist_path:
                    visualizations['distributions'] = dist_path

            # Correlation heatmap
            if len(numeric_cols) > 1:
                corr_path = self._plot_correlation_heatmap(df, numeric_cols)
                if corr_path:
                    visualizations['correlation_heatmap'] = corr_path

            # Missing values heatmap
            missing_path = self._plot_missing_values(df)
            if missing_path:
                visualizations['missing_values'] = missing_path

            # Target variable analysis if specified
            if target_column and target_column in df.columns:
                target_path = self._plot_target_analysis(df, target_column)
                if target_path:
                    visualizations['target_analysis'] = target_path

        except Exception as e:
            logger.warning(f"Error generating visualizations: {e}")

        return visualizations

    def _plot_distributions(self, df: pd.DataFrame, numeric_cols: List[str]) -> Optional[str]:
        """Plot distributions of numeric columns"""
        try:
            n_cols = min(len(numeric_cols), 4)
            n_rows = (len(numeric_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4 * n_rows))
            if n_rows == 1:
                axes = [axes] if n_cols == 1 else axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(numeric_cols[:16]):  # Limit to 16 plots
                df[col].hist(bins=30, ax=axes[i], alpha=0.7)
                axes[i].set_title(f'Distribution of {col}')
                axes[i].set_xlabel(col)
                axes[i].set_ylabel('Frequency')

            # Hide empty subplots
            for i in range(len(numeric_cols), len(axes)):
                axes[i].set_visible(False)

            plt.tight_layout()

            chart_path = os.path.join(self.charts_dir, f'distributions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting distributions: {e}")
            return None

    def _plot_correlation_heatmap(self, df: pd.DataFrame, numeric_cols: List[str]) -> Optional[str]:
        """Plot correlation heatmap"""
        try:
            plt.figure(figsize=(10, 8))
            corr_matrix = df[numeric_cols].corr()

            sns.heatmap(
                corr_matrix,
                annot=True,
                cmap='coolwarm',
                center=0,
                square=True,
                fmt='.2f'
            )
            plt.title('Correlation Matrix')
            plt.tight_layout()

            chart_path = os.path.join(self.charts_dir, f'correlation_heatmap_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting correlation heatmap: {e}")
            return None

    def _plot_missing_values(self, df: pd.DataFrame) -> Optional[str]:
        """Plot missing values heatmap"""
        try:
            missing_data = df.isnull()

            if not missing_data.any().any():
                return None  # No missing values

            plt.figure(figsize=(12, 6))
            sns.heatmap(missing_data, cbar=True, yticklabels=False, cmap='viridis')
            plt.title('Missing Values Heatmap')
            plt.xlabel('Columns')
            plt.tight_layout()

            chart_path = os.path.join(self.charts_dir, f'missing_values_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting missing values: {e}")
            return None

    def _plot_target_analysis(self, df: pd.DataFrame, target_column: str) -> Optional[str]:
        """Plot target variable analysis"""
        try:
            plt.figure(figsize=(12, 4))

            # Check if target is numeric or categorical
            if pd.api.types.is_numeric_dtype(df[target_column]):
                # Numeric target - histogram
                plt.subplot(1, 2, 1)
                df[target_column].hist(bins=30, alpha=0.7)
                plt.title(f'Distribution of {target_column}')
                plt.xlabel(target_column)
                plt.ylabel('Frequency')

                # Box plot
                plt.subplot(1, 2, 2)
                df.boxplot(column=target_column, ax=plt.gca())
                plt.title(f'Box Plot of {target_column}')
            else:
                # Categorical target - value counts
                plt.subplot(1, 2, 1)
                value_counts = df[target_column].value_counts()
                value_counts.plot(kind='bar')
                plt.title(f'Distribution of {target_column}')
                plt.xlabel(target_column)
                plt.ylabel('Count')
                plt.xticks(rotation=45)

                # Pie chart
                plt.subplot(1, 2, 2)
                value_counts.plot(kind='pie', autopct='%1.1f%%')
                plt.title(f'Proportion of {target_column}')
                plt.ylabel('')

            plt.tight_layout()

            chart_path = os.path.join(self.charts_dir, f'target_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            return chart_path
        except Exception as e:
            logger.error(f"Error plotting target analysis: {e}")
            return None

    def _generate_insights_summary(
        self,
        df: pd.DataFrame,
        basic_stats: Dict[str, Any],
        missing_values: Dict[str, int],
        outliers: Dict[str, int],
        target_column: Optional[str] = None
    ) -> str:
        """Generate human-readable insights summary"""
        summary_parts = []

        # Dataset overview
        total_rows = basic_stats['overall']['total_rows']
        total_cols = basic_stats['overall']['total_columns']
        summary_parts.append(f"Dataset contains {total_rows:,} rows and {total_cols} columns")

        # Data types
        numeric_cols = basic_stats['overall']['numeric_columns']
        categorical_cols = basic_stats['overall']['categorical_columns']
        summary_parts.append(f"Data types: {numeric_cols} numeric, {categorical_cols} categorical columns")

        # Missing values
        total_missing = sum(missing_values.values())
        if total_missing > 0:
            missing_percentage = (total_missing / (total_rows * total_cols)) * 100
            summary_parts.append(f"Missing values: {total_missing:,} ({missing_percentage:.1f}% of total data)")

            # Columns with most missing values
            missing_cols = {k: v for k, v in missing_values.items() if v > 0}
            if missing_cols:
                worst_col = max(missing_cols, key=missing_cols.get)
                worst_percentage = (missing_cols[worst_col] / total_rows) * 100
                summary_parts.append(f"Column '{worst_col}' has the most missing values ({worst_percentage:.1f}%)")
        else:
            summary_parts.append("No missing values detected")

        # Outliers
        total_outliers = sum(outliers.values())
        if total_outliers > 0:
            summary_parts.append(f"Detected {total_outliers:,} outliers across numeric columns")

            # Column with most outliers
            if outliers:
                outlier_col = max(outliers, key=outliers.get)
                if outliers[outlier_col] > 0:
                    outlier_percentage = (outliers[outlier_col] / total_rows) * 100
                    summary_parts.append(f"Column '{outlier_col}' has the most outliers ({outlier_percentage:.1f}%)")

        # Target column insights
        if target_column and target_column in df.columns:
            target_unique = df[target_column].nunique()
            target_missing = missing_values.get(target_column, 0)

            if pd.api.types.is_numeric_dtype(df[target_column]):
                target_type = "numeric (regression problem)"
            else:
                target_type = f"categorical with {target_unique} classes (classification problem)"

            summary_parts.append(f"Target column '{target_column}' is {target_type}")

            if target_missing > 0:
                summary_parts.append(f"Target column has {target_missing} missing values")

        # Memory usage
        memory_mb = basic_stats['overall']['memory_usage_mb']
        summary_parts.append(f"Dataset memory usage: {memory_mb:.1f} MB")

        return ". ".join(summary_parts)


# Celery task
@task(name="get_insights")
def get_insights_task(dataset_path: str, target_column: Optional[str] = None) -> Dict[str, Any]:
    """Celery task for data insights generation"""
    try:
        logger.info("Starting data insights generation task")

        generator = InsightsGenerator()
        result = generator.generate_insights(dataset_path, target_column)

        logger.info("Data insights generation task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in data insights generation task: {e}")
        raise


# Celery task
@task(name="get_insights")
def get_insights_task(*args, **kwargs) -> Dict[str, Any]:
    """Celery task for the service"""
    try:
        logger.info("Starting task")

        service = InsightsGenerator()
        result = service.generate_insights(*args, **kwargs)

        logger.info("Task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in task: {e}")
        raise
