"""
Data cleaning tool for the AI Data Science Pipeline
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
import sys
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer, KNNImputer

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, DATA_CLEANING_CONFIG
from mcp_server.models import DataCleaningResult
from mcp_server.celery import task


class DataCleaner:
    """Advanced data cleaning service"""

    def __init__(self):
        self.config = DATA_CLEANING_CONFIG
        self.upload_dir = settings.UPLOAD_DIR

    def clean_dataset(self, dataset_path: str, cleaning_options: Optional[Dict[str, Any]] = None) -> DataCleaningResult:
        """Clean a dataset with comprehensive preprocessing"""
        logger.info(f"Starting data cleaning for: {dataset_path}")

        # Load dataset
        df = self._load_dataset(dataset_path)
        original_shape = df.shape

        # Apply cleaning options or use defaults
        options = cleaning_options or {}

        # Track cleaning operations
        cleaning_log = []
        dropped_columns = []
        missing_values_handled = {}
        outliers_removed = 0
        duplicates_removed = 0

        # 1. Remove duplicates
        initial_rows = len(df)
        df = df.drop_duplicates()
        duplicates_removed = initial_rows - len(df)
        if duplicates_removed > 0:
            cleaning_log.append(f"Removed {duplicates_removed} duplicate rows")

        # 2. Handle missing values
        df, missing_handling = self._handle_missing_values(df, options)
        missing_values_handled.update(missing_handling)
        cleaning_log.extend([f"Missing values in {col}: {method}" for col, method in missing_handling.items()])

        # 3. Remove columns with too many missing values
        df, high_missing_cols = self._remove_high_missing_columns(df, options)
        dropped_columns.extend(high_missing_cols)
        if high_missing_cols:
            cleaning_log.append(f"Dropped columns with >50% missing values: {high_missing_cols}")

        # 4. Remove low variance columns
        df, low_var_cols = self._remove_low_variance_columns(df, options)
        dropped_columns.extend(low_var_cols)
        if low_var_cols:
            cleaning_log.append(f"Dropped low variance columns: {low_var_cols}")

        # 5. Remove highly correlated columns
        df, corr_cols = self._remove_highly_correlated_columns(df, options)
        dropped_columns.extend(corr_cols)
        if corr_cols:
            cleaning_log.append(f"Dropped highly correlated columns: {corr_cols}")

        # 6. Handle outliers
        df, outliers_count = self._handle_outliers(df, options)
        outliers_removed = outliers_count
        if outliers_removed > 0:
            cleaning_log.append(f"Removed {outliers_removed} outlier rows")

        # 7. Encode categorical variables
        df = self._encode_categorical_variables(df, options)
        cleaning_log.append("Encoded categorical variables")

        # 8. Normalize/standardize numerical columns
        df = self._normalize_numerical_columns(df, options)
        cleaning_log.append("Normalized numerical columns")

        # Save cleaned dataset
        cleaned_data_path = self._save_cleaned_dataset(df, dataset_path)

        # Generate summary
        cleaning_summary = "; ".join(cleaning_log)

        result = DataCleaningResult(
            original_shape=original_shape,
            cleaned_shape=df.shape,
            dropped_columns=dropped_columns,
            missing_values_handled=missing_values_handled,
            outliers_removed=outliers_removed,
            duplicates_removed=duplicates_removed,
            cleaning_summary=cleaning_summary,
            cleaned_data_path=cleaned_data_path
        )

        logger.info(f"Data cleaning completed. Shape: {original_shape} -> {df.shape}")
        return result