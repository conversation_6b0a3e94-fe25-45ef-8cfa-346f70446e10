"""
Configuration settings for the AI Data Science Pipeline
"""
import os
from typing import Dict, List, Any
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    """Application settings"""

    # API Configuration
    API_HOST: str = Field(default="0.0.0.0", env="API_HOST")
    API_PORT: int = Field(default=8000, env="API_PORT")
    DEBUG: bool = Field(default=True, env="DEBUG")

    # Database Configuration
    DATABASE_URL: str = Field(default="postgresql://user:password@localhost:5432/predictive_ai", env="DATABASE_URL")

    # Redis Configuration (for Celery)
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")

    # Celery Configuration
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/0", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/0", env="CELERY_RESULT_BACKEND")
    CELERY_CONCURRENCY: int = Field(default=3, env="CELERY_CONCURRENCY")

    # LLM Configuration
    OPENAI_API_KEY: str = Field(default="", env="OPENAI_API_KEY")
    GROQ_API_KEY: str = Field(default="", env="GROQ_API_KEY")
    DEFAULT_LLM_MODEL: str = Field(default="gpt-3.5-turbo", env="DEFAULT_LLM_MODEL")
    FALLBACK_LLM_MODEL: str = Field(default="mixtral-8x7b-32768", env="FALLBACK_LLM_MODEL")

    # File Upload Configuration
    UPLOAD_DIR: str = Field(default="./uploads", env="UPLOAD_DIR")
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    ALLOWED_EXTENSIONS: List[str] = Field(default=["csv", "xlsx", "xls", "txt", "json"], env="ALLOWED_EXTENSIONS")

    # Model Training Configuration
    MODEL_SAVE_DIR: str = Field(default="./models", env="MODEL_SAVE_DIR")
    MAX_TRAINING_TIME: int = Field(default=3600, env="MAX_TRAINING_TIME")  # 1 hour

    # Logging Configuration
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="./logs/app.log", env="LOG_FILE")

    class Config:
        env_file = ".env"
        case_sensitive = True


# Problem Type Configuration
PROBLEM_TYPES = {
    "regression": {
        "description": "Predicting continuous numerical values",
        "models": ["linear_regression", "random_forest", "xgboost", "lightgbm", "catboost"],
        "metrics": ["mse", "rmse", "mae", "r2_score"]
    },
    "classification": {
        "description": "Predicting categorical labels",
        "models": ["logistic_regression", "random_forest", "xgboost", "lightgbm", "catboost", "svm"],
        "metrics": ["accuracy", "precision", "recall", "f1_score", "roc_auc"]
    },
    "time_series": {
        "description": "Predicting future values based on time-ordered data",
        "models": ["arima", "prophet", "lstm", "xgboost_ts"],
        "metrics": ["mape", "smape", "mae", "rmse"]
    },
    "clustering": {
        "description": "Grouping similar data points together",
        "models": ["kmeans", "dbscan", "hierarchical", "gaussian_mixture"],
        "metrics": ["silhouette_score", "calinski_harabasz", "davies_bouldin"]
    }
}

# Model Configuration
MODEL_CONFIGS = {
    "linear_regression": {
        "class": "sklearn.linear_model.LinearRegression",
        "hyperparams": {}
    },
    "logistic_regression": {
        "class": "sklearn.linear_model.LogisticRegression",
        "hyperparams": {"max_iter": 1000, "random_state": 42}
    },
    "random_forest": {
        "class": "sklearn.ensemble.RandomForestRegressor",
        "hyperparams": {"n_estimators": 100, "random_state": 42}
    },
    "xgboost": {
        "class": "xgboost.XGBRegressor",
        "hyperparams": {"random_state": 42, "eval_metric": "rmse"}
    },
    "lightgbm": {
        "class": "lightgbm.LGBMRegressor",
        "hyperparams": {"random_state": 42, "verbose": -1}
    },
    "catboost": {
        "class": "catboost.CatBoostRegressor",
        "hyperparams": {"random_state": 42, "verbose": False}
    }
}

# Data Cleaning Configuration
DATA_CLEANING_CONFIG = {
    "missing_value_threshold": 0.5,  # Drop columns with >50% missing values
    "correlation_threshold": 0.95,   # Drop highly correlated features
    "variance_threshold": 0.01,      # Drop low variance features
    "outlier_method": "iqr",         # Method for outlier detection
    "outlier_threshold": 1.5         # IQR multiplier for outlier detection
}

# Hyperparameter Tuning Configuration
HYPERPARAM_TUNING_CONFIG = {
    "cv_folds": 5,
    "n_trials": 50,
    "timeout": 1800,  # 30 minutes
    "random_state": 42
}

# Create settings instance
settings = Settings()

# Ensure directories exist
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.MODEL_SAVE_DIR, exist_ok=True)
os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)