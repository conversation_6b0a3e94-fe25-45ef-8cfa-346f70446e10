"""
Multiple model training tool for the AI Data Science Pipeline
"""
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
from loguru import logger
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import ModelTrainingResult
from mcp_server.celery import task
from mcp_server.tools.train_model import ModelTrainer


class MultiModelTrainer:
    """Advanced multi-model training and comparison service"""

    def __init__(self):
        self.model_trainer = ModelTrainer()
        self.problem_types = PROBLEM_TYPES

    def train_multiple_models(
        self,
        dataset_path: str,
        problem_type: str,
        target_column: str,
        feature_columns: Optional[List[str]] = None,
        models_to_train: Optional[List[str]] = None,
        test_size: float = 0.2,
        random_state: int = 42
    ) -> Dict[str, Any]:
        """Train multiple models and compare their performance"""
        logger.info(f"Starting multi-model training for {problem_type} problem")

        start_time = datetime.now()

        # Get default models for problem type if not specified
        if not models_to_train:
            models_to_train = self._get_default_models(problem_type)

        logger.info(f"Training models: {models_to_train}")

        # Train models in parallel
        results = self._train_models_parallel(
            dataset_path=dataset_path,
            problem_type=problem_type,
            target_column=target_column,
            feature_columns=feature_columns,
            models_to_train=models_to_train,
            test_size=test_size,
            random_state=random_state
        )

        # Find best model
        best_model = self._find_best_model(results, problem_type)

        # Generate comparison summary
        comparison_summary = self._generate_comparison_summary(results, best_model, problem_type)

        total_time = (datetime.now() - start_time).total_seconds()

        result = {
            "results": results,
            "best_model": best_model,
            "comparison_summary": comparison_summary,
            "total_training_time": total_time,
            "models_trained": len(results)
        }

        logger.info(f"Multi-model training completed in {total_time:.2f} seconds")
        return result

    def _get_default_models(self, problem_type: str) -> List[str]:
        """Get default models for the problem type"""
        if problem_type == "classification":
            return ["logistic_regression", "random_forest", "svm"]
        elif problem_type == "regression":
            return ["linear_regression", "random_forest", "svm"]
        elif problem_type == "time_series":
            return ["random_forest"]  # Simplified for now
        else:
            return ["random_forest"]

    def _train_models_parallel(
        self,
        dataset_path: str,
        problem_type: str,
        target_column: str,
        feature_columns: Optional[List[str]],
        models_to_train: List[str],
        test_size: float,
        random_state: int
    ) -> List[Dict[str, Any]]:
        """Train multiple models in parallel"""
        results = []

        # Use ThreadPoolExecutor for parallel training
        with ThreadPoolExecutor(max_workers=min(len(models_to_train), 3)) as executor:
            # Submit training tasks
            future_to_model = {
                executor.submit(
                    self._train_single_model,
                    dataset_path,
                    problem_type,
                    target_column,
                    model_name,
                    feature_columns,
                    test_size,
                    random_state
                ): model_name
                for model_name in models_to_train
            }

            # Collect results
            for future in as_completed(future_to_model):
                model_name = future_to_model[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Completed training: {model_name}")
                except Exception as e:
                    logger.error(f"Error training {model_name}: {e}")
                    # Add failed result
                    results.append({
                        "model_name": model_name,
                        "error": str(e),
                        "training_time": 0,
                        "metrics": {}
                    })

        return results

    def _train_single_model(
        self,
        dataset_path: str,
        problem_type: str,
        target_column: str,
        model_name: str,
        feature_columns: Optional[List[str]],
        test_size: float,
        random_state: int
    ) -> Dict[str, Any]:
        """Train a single model"""
        try:
            result = self.model_trainer.train_model(
                dataset_path=dataset_path,
                problem_type=problem_type,
                target_column=target_column,
                model_name=model_name,
                feature_columns=feature_columns,
                test_size=test_size,
                random_state=random_state
            )
            return result
        except Exception as e:
            logger.error(f"Error training {model_name}: {e}")
            raise

    def _find_best_model(self, results: List[Dict[str, Any]], problem_type: str) -> Dict[str, Any]:
        """Find the best performing model"""
        valid_results = [r for r in results if 'error' not in r and r.get('metrics')]

        if not valid_results:
            raise ValueError("No models trained successfully")

        # Define scoring metric based on problem type
        if problem_type == "classification":
            # Use F1 score, fallback to accuracy
            best_model = max(valid_results, key=lambda x: x['metrics'].get('f1_score', x['metrics'].get('accuracy', 0)))
        elif problem_type == "regression":
            # Use R² score (higher is better), fallback to negative RMSE
            best_model = max(valid_results, key=lambda x: x['metrics'].get('r2_score', -x['metrics'].get('rmse', float('inf'))))
        else:
            # Default to first model
            best_model = valid_results[0]

        logger.info(f"Best model: {best_model['model_name']}")
        return best_model

    def _generate_comparison_summary(
        self,
        results: List[Dict[str, Any]],
        best_model: Dict[str, Any],
        problem_type: str
    ) -> str:
        """Generate a summary comparing all models"""
        summary_parts = []

        valid_results = [r for r in results if 'error' not in r]
        failed_results = [r for r in results if 'error' in r]

        summary_parts.append(f"Trained {len(valid_results)} models successfully")

        if failed_results:
            failed_names = [r['model_name'] for r in failed_results]
            summary_parts.append(f"Failed to train: {', '.join(failed_names)}")

        if valid_results:
            summary_parts.append(f"Best performing model: {best_model['model_name']}")

            # Add performance details
            if problem_type == "classification":
                if 'accuracy' in best_model['metrics']:
                    summary_parts.append(f"Best accuracy: {best_model['metrics']['accuracy']:.4f}")
                if 'f1_score' in best_model['metrics']:
                    summary_parts.append(f"Best F1 score: {best_model['metrics']['f1_score']:.4f}")

            elif problem_type == "regression":
                if 'r2_score' in best_model['metrics']:
                    summary_parts.append(f"Best R² score: {best_model['metrics']['r2_score']:.4f}")
                if 'rmse' in best_model['metrics']:
                    summary_parts.append(f"Best RMSE: {best_model['metrics']['rmse']:.4f}")

            # Training time summary
            total_time = sum(r.get('training_time', 0) for r in valid_results)
            summary_parts.append(f"Total training time: {total_time:.2f} seconds")

            # Model ranking
            if len(valid_results) > 1:
                ranking = self._rank_models(valid_results, problem_type)
                ranking_str = ", ".join([f"{i+1}. {model}" for i, model in enumerate(ranking)])
                summary_parts.append(f"Model ranking: {ranking_str}")

        return ". ".join(summary_parts)

    def _rank_models(self, results: List[Dict[str, Any]], problem_type: str) -> List[str]:
        """Rank models by performance"""
        if problem_type == "classification":
            # Sort by F1 score, then accuracy
            sorted_results = sorted(
                results,
                key=lambda x: (x['metrics'].get('f1_score', 0), x['metrics'].get('accuracy', 0)),
                reverse=True
            )
        elif problem_type == "regression":
            # Sort by R² score
            sorted_results = sorted(
                results,
                key=lambda x: x['metrics'].get('r2_score', -float('inf')),
                reverse=True
            )
        else:
            sorted_results = results

        return [r['model_name'] for r in sorted_results]


# Celery task
@task(name="train_multiple_models")
def train_multiple_models_task(
    dataset_path: str,
    problem_type: str,
    target_column: str,
    feature_columns: Optional[List[str]] = None,
    models_to_train: Optional[List[str]] = None,
    test_size: float = 0.2,
    random_state: int = 42
) -> Dict[str, Any]:
    """Celery task for multi-model training"""
    try:
        logger.info("Starting multi-model training task")

        trainer = MultiModelTrainer()
        result = trainer.train_multiple_models(
            dataset_path=dataset_path,
            problem_type=problem_type,
            target_column=target_column,
            feature_columns=feature_columns,
            models_to_train=models_to_train,
            test_size=test_size,
            random_state=random_state
        )

        logger.info("Multi-model training task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in multi-model training task: {e}")
        raise


# Celery task
@task(name="train_multiple_models")
def train_multiple_models_task(*args, **kwargs) -> Dict[str, Any]:
    """Celery task for the service"""
    try:
        logger.info("Starting task")

        service = MultiModelTrainer()
        result = service.train_multiple_models(*args, **kwargs)

        logger.info("Task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error in task: {e}")
        raise
