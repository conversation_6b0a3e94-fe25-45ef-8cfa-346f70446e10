"""
Streamlit UI for AI Data Science Pipeline
Simple, integrated interface for the data science workflow
"""
import streamlit as st
import requests
import pandas as pd
import json
import sys
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings

# Configure Streamlit page
st.set_page_config(
    page_title="AI Data Science Pipeline",
    page_icon="🤖",
    layout="wide"
)

# API endpoints
API_BASE_URL = f"http://{settings.API_HOST}:{settings.API_PORT}"

# Initialize session state
if 'step' not in st.session_state:
    st.session_state.step = 'data_source'
if 'pipeline_id' not in st.session_state:
    st.session_state.pipeline_id = None
if 'user_query' not in st.session_state:
    st.session_state.user_query = ""
if 'data_source_type' not in st.session_state:
    st.session_state.data_source_type = None
if 'selected_dataset' not in st.session_state:
    st.session_state.selected_dataset = None
if 'available_datasets' not in st.session_state:
    st.session_state.available_datasets = []
if 'pipeline_steps' not in st.session_state:
    st.session_state.pipeline_steps = []
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'db_connection' not in st.session_state:
    st.session_state.db_connection = None
if 'uploaded_file_path' not in st.session_state:
    st.session_state.uploaded_file_path = None

# Simple CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f77b4, #ff7f0e);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    .step-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    .chat-msg {
        padding: 0.5rem;
        margin: 0.5rem 0;
        border-radius: 0.3rem;
    }
    .user-msg {
        background-color: #e3f2fd;
        text-align: right;
    }
    .ai-msg {
        background-color: #f5f5f5;
    }
    .dataset-card {
        border: 1px solid #ddd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .recommended {
        border-color: #28a745;
        background-color: #d4edda;
    }
</style>
""", unsafe_allow_html=True)

# Utility functions
def make_api_request(endpoint: str, method: str = "GET", data: Dict = None, files: Dict = None) -> Dict[str, Any]:
    """Make API request to the orchestrator"""
    url = f"{API_BASE_URL}{endpoint}"
    try:
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            if files:
                response = requests.post(url, files=files, timeout=60)
            else:
                response = requests.post(url, json=data, timeout=60)

        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def display_chat_sidebar():
    """Display simple chat interface in sidebar"""
    with st.sidebar:
        st.subheader("💬 AI Assistant")

        # Current context
        if st.session_state.step:
            st.info(f"Current Step: {st.session_state.step.replace('_', ' ').title()}")

        # Chat history (last 3 messages)
        if st.session_state.chat_history:
            st.write("**Recent Chat:**")
            for msg in st.session_state.chat_history[-3:]:
                if msg["type"] == "user":
                    st.markdown(f'<div class="chat-msg user-msg">You: {msg["content"]}</div>', unsafe_allow_html=True)
                else:
                    st.markdown(f'<div class="chat-msg ai-msg">AI: {msg["content"]}</div>', unsafe_allow_html=True)

        # Chat input
        user_message = st.text_input("Ask AI:", placeholder="How can I help?")
        if st.button("Send") and user_message.strip():
            # Add user message
            st.session_state.chat_history.append({
                "type": "user",
                "content": user_message,
                "timestamp": datetime.now()
            })

            # Get AI response
            chat_data = {
                "message": user_message,
                "pipeline_id": st.session_state.pipeline_id or "",
                "context": {
                    "current_step": st.session_state.step,
                    "user_query": st.session_state.user_query,
                    "data_source": st.session_state.data_source_type
                }
            }

            result = make_api_request("/chat", method="POST", data=chat_data)

            if "error" not in result:
                ai_response = result.get("response", "I couldn't process your request.")
                st.session_state.chat_history.append({
                    "type": "ai",
                    "content": ai_response,
                    "timestamp": datetime.now()
                })
            else:
                st.session_state.chat_history.append({
                    "type": "ai",
                    "content": "I'm having technical difficulties. Please try again.",
                    "timestamp": datetime.now()
                })

            st.rerun()

def step_1_data_source():
    """Step 1: Choose data source using dropdown"""
    st.markdown("""
    <div class="main-header">
        <h1>🤖 AI Data Science Pipeline</h1>
        <p>Choose your data source to begin</p>
    </div>
    """, unsafe_allow_html=True)

    st.markdown("""
    <div class="step-card">
        <h3>Step 1: Select Data Source</h3>
        <p>Choose how you want to provide your data for analysis</p>
    </div>
    """, unsafe_allow_html=True)

    # Dropdown for data source selection
    data_source_option = st.selectbox(
        "Select your data source:",
        ["-- Select an option --", "Database (PostgreSQL/MongoDB)", "Upload File (CSV/Excel/JSON)"],
        key="data_source_dropdown"
    )

    if data_source_option == "Database (PostgreSQL/MongoDB)":
        st.session_state.data_source_type = "database"
        if st.button("Continue with Database", type="primary"):
            st.session_state.step = "database_setup"
            st.rerun()

    elif data_source_option == "Upload File (CSV/Excel/JSON)":
        st.session_state.data_source_type = "file"
        if st.button("Continue with File Upload", type="primary"):
            st.session_state.step = "file_upload"
            st.rerun()

def step_2_database_setup():
    """Step 2: Database connection and dataset discovery"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 2: Database Connection</h3>
        <p>Connect to your database and discover available datasets</p>
    </div>
    """, unsafe_allow_html=True)

    # Database type selection
    db_type = st.selectbox("Database Type:", ["PostgreSQL", "MongoDB"])

    # Connection form
    with st.form("db_connection"):
        col1, col2 = st.columns(2)
        with col1:
            host = st.text_input("Host", value="localhost")
            database = st.text_input("Database Name")
        with col2:
            port = st.number_input("Port", value=5432 if db_type == "PostgreSQL" else 27017)
            username = st.text_input("Username")

        password = st.text_input("Password", type="password")

        if st.form_submit_button("Connect and Discover Datasets", type="primary"):
            if not all([host, database, username, password]):
                st.error("Please fill in all connection details.")
                return

            with st.spinner("Connecting to database and discovering datasets..."):
                # Store connection details
                st.session_state.db_connection = {
                    "type": db_type,
                    "host": host,
                    "port": port,
                    "database": database,
                    "username": username,
                    "password": password
                }

                # Call MCP server to discover datasets
                discovery_data = {
                    "db_type": db_type,
                    "connection_details": {
                        "host": host,
                        "port": port,
                        "database": database,
                        "username": username,
                        "password": password
                    }
                }

                result = make_api_request("/discover_datasets", method="POST", data=discovery_data)

                if "error" not in result:
                    datasets = result.get("datasets", [])
                    st.session_state.available_datasets = datasets
                    st.success(f"✅ Connected successfully! Found {len(datasets)} datasets.")
                else:
                    # Fallback to mock data for demo purposes
                    mock_datasets = [
                        {
                            "name": "customers",
                            "description": "Customer information and demographics",
                            "rows": 10000,
                            "columns": ["customer_id", "name", "age", "gender", "city", "signup_date", "total_purchases"]
                        },
                        {
                            "name": "orders",
                            "description": "Order transactions and details",
                            "rows": 50000,
                            "columns": ["order_id", "customer_id", "product_id", "quantity", "price", "order_date", "status"]
                        },
                        {
                            "name": "products",
                            "description": "Product catalog and information",
                            "rows": 1000,
                            "columns": ["product_id", "name", "category", "price", "stock", "rating", "description"]
                        },
                        {
                            "name": "user_activity",
                            "description": "User website activity and behavior",
                            "rows": 75000,
                            "columns": ["user_id", "session_id", "page_views", "time_spent", "clicks", "bounce_rate"]
                        }
                    ]

                    st.session_state.available_datasets = mock_datasets
                    st.warning(f"⚠️ Using demo datasets. Database connection failed: {result.get('error', 'Unknown error')}")
                    st.success(f"📊 Loaded {len(mock_datasets)} demo datasets for testing.")
                time.sleep(1)
                st.session_state.step = "dataset_selection"
                st.rerun()

    if st.button("← Back to Data Source"):
        st.session_state.step = "data_source"
        st.rerun()

def step_2_file_upload():
    """Step 2: File upload"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 2: Upload Your Dataset</h3>
        <p>Upload your data file for analysis</p>
    </div>
    """, unsafe_allow_html=True)

    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['csv', 'xlsx', 'xls', 'json'],
        help="Supported formats: CSV, Excel, JSON"
    )

    if uploaded_file is not None:
        # Display file info
        st.write("**File Information:**")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Filename", uploaded_file.name)
        with col2:
            st.metric("Size", f"{uploaded_file.size / 1024:.1f} KB")
        with col3:
            st.metric("Type", uploaded_file.type)

        # File preview
        try:
            if uploaded_file.name.endswith('.csv'):
                df_preview = pd.read_csv(uploaded_file, nrows=5)
                st.write("**Preview (first 5 rows):**")
                st.dataframe(df_preview)
                uploaded_file.seek(0)  # Reset file pointer
        except Exception as e:
            st.warning(f"Could not preview file: {str(e)}")

        # Upload button
        if st.button("Upload and Continue", type="primary"):
            with st.spinner("Uploading file..."):
                files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
                result = make_api_request("/upload_file", method="POST", files=files)

                if "error" not in result:
                    st.success("✅ File uploaded successfully!")
                    st.session_state.uploaded_file_path = result.get("file_path")
                    st.session_state.selected_dataset = {
                        "name": uploaded_file.name,
                        "path": result.get("file_path"),
                        "type": "uploaded_file"
                    }
                    time.sleep(1)
                    st.session_state.step = "query_input"
                    st.rerun()
                else:
                    st.error(f"❌ Upload failed: {result['error']}")

    if st.button("← Back to Data Source"):
        st.session_state.step = "data_source"
        st.rerun()

def step_3_dataset_selection():
    """Step 3: Display datasets and get AI recommendations"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 3: Dataset Selection</h3>
        <p>Review available datasets and get AI recommendations</p>
    </div>
    """, unsafe_allow_html=True)

    # Show available datasets
    st.subheader("Available Datasets")

    for i, dataset in enumerate(st.session_state.available_datasets):
        with st.expander(f"📊 {dataset['name']} ({dataset['rows']:,} rows)"):
            st.write(f"**Description:** {dataset['description']}")
            st.write(f"**Columns:** {', '.join(dataset['columns'])}")

            if st.button(f"Select {dataset['name']}", key=f"select_{i}"):
                st.session_state.selected_dataset = dataset
                st.session_state.step = "query_input"
                st.rerun()

    # Query input for recommendations
    st.subheader("Get AI Dataset Recommendation")
    user_query = st.text_area(
        "Describe what you want to achieve:",
        placeholder="e.g., I want to predict customer churn based on their behavior...",
        height=100
    )

    if st.button("Get AI Recommendation", type="primary") and user_query.strip():
        st.session_state.user_query = user_query

        with st.spinner("AI is analyzing your requirements and recommending datasets..."):
            # Call MCP server for dataset recommendation
            recommendation_data = {
                "user_query": user_query,
                "available_datasets": st.session_state.available_datasets
            }

            result = make_api_request("/suggest_datasets", method="POST", data=recommendation_data)

            if "error" not in result:
                recommended_datasets = result.get("recommended_datasets", [])

                st.subheader("🤖 AI Recommendations")
                for i, rec in enumerate(recommended_datasets):
                    dataset_name = rec.get("dataset_name", "Unknown")
                    confidence = rec.get("confidence", 0)
                    reasoning = rec.get("reasoning", "No reasoning provided")

                    # Find the full dataset info
                    dataset_info = next((ds for ds in st.session_state.available_datasets if ds["name"] == dataset_name), None)

                    if dataset_info:
                        card_class = "dataset-card recommended" if i == 0 else "dataset-card"
                        st.markdown(f"""
                        <div class="{card_class}">
                            <h4>{'🥇 ' if i == 0 else ''}Recommendation {i+1}: {dataset_name}</h4>
                            <p><strong>Confidence:</strong> {confidence:.1%}</p>
                            <p><strong>Reasoning:</strong> {reasoning}</p>
                            <p><strong>Columns:</strong> {', '.join(dataset_info['columns'])}</p>
                        </div>
                        """, unsafe_allow_html=True)

                        if st.button(f"Select {dataset_name}", key=f"rec_select_{i}", type="primary" if i == 0 else "secondary"):
                            st.session_state.selected_dataset = dataset_info
                            st.session_state.step = "query_input"
                            st.rerun()
            else:
                st.error(f"Failed to get recommendations: {result['error']}")

    if st.button("← Back to Database Setup"):
        st.session_state.step = "database_setup"
        st.rerun()

def step_4_query_input():
    """Step 4: User query input and pipeline start"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 4: Define Your Analysis Goal</h3>
        <p>Describe what you want to achieve with your data</p>
    </div>
    """, unsafe_allow_html=True)

    # Show selected dataset
    if st.session_state.selected_dataset:
        dataset = st.session_state.selected_dataset
        st.info(f"**Selected Dataset:** {dataset['name']}")

        if dataset.get("columns"):
            st.write(f"**Available Columns:** {', '.join(dataset['columns'])}")

    # User query input
    user_query = st.text_area(
        "Describe your analysis goal:",
        value=st.session_state.user_query,
        placeholder="Example: I want to predict which customers will churn based on their purchase history and demographics...",
        height=120
    )

    # Optional target column
    target_column = st.text_input(
        "Target Column (optional):",
        help="If you know which column contains the values you want to predict"
    )

    if st.button("Start AI Pipeline", type="primary") and user_query.strip():
        st.session_state.user_query = user_query

        with st.spinner("Starting AI pipeline..."):
            # Prepare pipeline data
            pipeline_data = {
                "user_request": user_query,
                "target_column": target_column if target_column else None
            }

            # Add dataset path based on source type
            if st.session_state.data_source_type == "file":
                pipeline_data["dataset_path"] = st.session_state.uploaded_file_path
            else:
                # For database, we'll use the selected dataset info
                pipeline_data["dataset_info"] = st.session_state.selected_dataset
                pipeline_data["db_connection"] = st.session_state.db_connection

            result = make_api_request("/start_pipeline", method="POST", data=pipeline_data)

            if "error" not in result:
                st.session_state.pipeline_id = result.get("pipeline_id")
                st.success("✅ Pipeline started successfully!")
                time.sleep(1)
                st.session_state.step = "pipeline_execution"
                st.rerun()
            else:
                st.error(f"❌ Failed to start pipeline: {result['error']}")

    # Back button
    if st.button("← Back"):
        if st.session_state.data_source_type == "database":
            st.session_state.step = "dataset_selection"
        else:
            st.session_state.step = "file_upload"
        st.rerun()

def step_5_pipeline_execution():
    """Step 5: Pipeline execution with step-by-step approval"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 5: AI Pipeline Execution</h3>
        <p>Watch AI process your data step by step</p>
    </div>
    """, unsafe_allow_html=True)

    # Pipeline info
    col1, col2 = st.columns(2)
    with col1:
        st.metric("Pipeline ID", st.session_state.pipeline_id[:8] + "..." if st.session_state.pipeline_id else "N/A")
    with col2:
        st.metric("Goal", st.session_state.user_query[:30] + "..." if len(st.session_state.user_query) > 30 else st.session_state.user_query)

    # Auto-refresh option
    auto_refresh = st.checkbox("Auto-refresh every 5 seconds")

    # Get pipeline status
    if st.button("🔄 Refresh Status") or auto_refresh or not st.session_state.pipeline_steps:
        with st.spinner("Getting pipeline status..."):
            result = make_api_request(f"/pipeline/{st.session_state.pipeline_id}/status")

            if "error" not in result:
                st.session_state.pipeline_steps = result.get("steps", [])
            else:
                st.error(f"Failed to get pipeline status: {result['error']}")
                return

    # Display pipeline steps
    if st.session_state.pipeline_steps:
        st.subheader("📋 Pipeline Steps")

        for i, step in enumerate(st.session_state.pipeline_steps):
            display_pipeline_step(i, step)
    else:
        st.info("Pipeline is initializing... Please wait and refresh.")

    # Auto-refresh logic
    if auto_refresh:
        time.sleep(5)
        st.rerun()

    # Reset button
    if st.button("🔄 Start New Pipeline"):
        reset_session()
        st.rerun()

def display_pipeline_step(step_index: int, step):
    """Display individual pipeline step with approval options"""
    # Safety check - ensure step is a dictionary
    if not isinstance(step, dict):
        st.error(f"Invalid step data format: {type(step)} - {step}")
        return

    step_name = step.get("step_name", "Unknown").replace("_", " ").title()
    status = step.get("status", "pending")

    # Status icons
    status_icons = {
        "completed": "✅",
        "running": "🔄",
        "failed": "❌",
        "pending": "⏳",
        "waiting_approval": "⏸️"
    }

    icon = status_icons.get(status, "❓")

    with st.expander(f"{icon} Step {step_index + 1}: {step_name} ({status.title()})", expanded=(status in ["waiting_approval", "failed"])):
        # Step details
        if step.get("started_at"):
            st.write(f"**Started:** {step['started_at']}")
        if step.get("completed_at"):
            st.write(f"**Completed:** {step['completed_at']}")
        if step.get("execution_time"):
            st.write(f"**Duration:** {step['execution_time']:.2f} seconds")

        # Show step results
        if step.get("output_data"):
            st.write("**Results:**")
            output_data = step["output_data"]

            # Display results based on step type
            if "problem_type" in step_name.lower():
                st.info(f"**Detected Problem Type:** {output_data.get('detected_problem_type', 'Unknown')}")
                st.write(f"**Confidence:** {output_data.get('confidence_score', 0):.2%}")
                st.write(f"**Reasoning:** {output_data.get('reasoning', 'No reasoning provided')}")

            elif "data_cleaning" in step_name.lower() or "cleaning" in step_name.lower():
                # Enhanced LLM-powered data cleaning display
                st.success(output_data.get("cleaning_summary", "Data cleaning completed"))

                # Cleaned data location and download
                if output_data.get("cleaned_data_path"):
                    cleaned_path = output_data["cleaned_data_path"]
                    st.info(f"📁 **Cleaned Dataset Saved:** `{cleaned_path}`")

                    # Download button for cleaned data
                    if os.path.exists(cleaned_path):
                        with open(cleaned_path, 'rb') as f:
                            cleaned_data = f.read()

                        st.download_button(
                            label="📥 Download Cleaned Dataset",
                            data=cleaned_data,
                            file_name=os.path.basename(cleaned_path),
                            mime="text/csv",
                            key=f"download_cleaned_{step_index}"
                        )
                    else:
                        st.warning("⚠️ Cleaned dataset file not found for download")

                col1, col2, col3 = st.columns(3)
                with col1:
                    original_shape = output_data.get("original_shape", (0, 0))
                    st.metric("Original Shape", f"{original_shape[0]} rows × {original_shape[1]} cols")
                with col2:
                    cleaned_shape = output_data.get("cleaned_shape", (0, 0))
                    st.metric("Cleaned Shape", f"{cleaned_shape[0]} rows × {cleaned_shape[1]} cols")
                with col3:
                    quality_score = output_data.get("data_quality_score", 0)
                    st.metric("Data Quality Score", f"{quality_score}%")

                # AI Insights Section
                if output_data.get("ai_insights"):
                    ai_insights = output_data["ai_insights"]
                    st.subheader("🤖 AI-Powered Analysis")

                    # Data Quality Assessment
                    if ai_insights.get("data_quality_assessment"):
                        st.info(f"**AI Assessment:** {ai_insights['data_quality_assessment']}")

                    # AI Cleaning Rationale
                    if ai_insights.get("cleaning_rationale"):
                        with st.expander("🧠 AI Cleaning Rationale"):
                            st.write(ai_insights["cleaning_rationale"])

                # Detailed Cleaning Actions
                if output_data.get("cleaning_actions"):
                    with st.expander("🔍 Detailed Cleaning Actions"):
                        for action in output_data["cleaning_actions"]:
                            action_type = action.get("type", "general")
                            icon = {"missing_values": "🔧", "outliers": "📊", "duplicates": "🔄"}.get(action_type, "•")
                            st.write(f"{icon} **{action.get('action', 'Unknown action')}**")
                            st.write(f"   *Reasoning:* {action.get('reasoning', 'No reasoning provided')}")

                # Feature Engineering Actions
                if output_data.get("feature_engineering_actions"):
                    with st.expander("⚙️ AI Feature Engineering"):
                        for action in output_data["feature_engineering_actions"]:
                            action_type = action.get("type", "general")
                            icon = {"feature_engineering": "🛠️", "transformation": "🔄"}.get(action_type, "•")
                            st.write(f"{icon} **{action.get('action', 'Unknown action')}**")
                            st.write(f"   *Reasoning:* {action.get('reasoning', 'No reasoning provided')}")

            elif "model_selection" in step_name.lower() or "selection" in step_name.lower():
                # Enhanced model selection display
                st.success("🤖 AI Model Recommendations Generated")

                # Model selection rationale
                if output_data.get("model_selection_rationale"):
                    st.info(f"**AI Rationale:** {output_data['model_selection_rationale']}")

                # Domain-specific considerations
                if output_data.get("domain_specific_considerations"):
                    with st.expander("🎯 Domain-Specific Considerations"):
                        st.write(output_data["domain_specific_considerations"])

                # Recommended models
                if output_data.get("recommended_models"):
                    st.subheader("🏆 Recommended Models")

                    for i, model in enumerate(output_data["recommended_models"]):
                        with st.expander(f"#{i+1} {model.get('model_name', 'Unknown Model')} (Score: {model.get('suitability_score', 0):.2f})"):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.write(f"**Type:** {model.get('model_type', 'Unknown')}")
                                st.write(f"**Interpretability:** {model.get('interpretability', 'Unknown')}")
                                st.write(f"**Training Time:** {model.get('training_time', 'Unknown')}")
                                st.write(f"**Prediction Speed:** {model.get('prediction_speed', 'Unknown')}")
                                st.write(f"**Data Requirements:** {model.get('data_requirements', 'Unknown')}")

                                if model.get("expected_performance"):
                                    st.write(f"**Expected Performance:** {model['expected_performance']}")

                            with col2:
                                if model.get("pros"):
                                    st.write("**✅ Advantages:**")
                                    for pro in model["pros"]:
                                        st.write(f"• {pro}")

                                if model.get("cons"):
                                    st.write("**❌ Disadvantages:**")
                                    for con in model["cons"]:
                                        st.write(f"• {con}")

                            if model.get("use_case_fit"):
                                st.write(f"**🎯 Use Case Fit:** {model['use_case_fit']}")

                            if model.get("domain_relevance"):
                                st.write(f"**🏢 Domain Relevance:** {model['domain_relevance']}")

                            if model.get("hyperparameters"):
                                st.write("**⚙️ Key Hyperparameters:**")
                                for param, value in model["hyperparameters"].items():
                                    st.write(f"• {param}: {value}")

                # Performance expectations
                if output_data.get("performance_expectations"):
                    with st.expander("📊 Performance Expectations"):
                        st.write(output_data["performance_expectations"])

                # Implementation recommendations
                if output_data.get("implementation_recommendations"):
                    with st.expander("🛠️ Implementation Recommendations"):
                        for rec in output_data["implementation_recommendations"]:
                            st.write(f"• {rec}")

                # Evaluation metrics
                if output_data.get("evaluation_metrics"):
                    with st.expander("📏 Recommended Evaluation Metrics"):
                        for metric in output_data["evaluation_metrics"]:
                            st.write(f"• {metric}")

            elif "train" in step_name.lower():
                # Enhanced model training display
                if output_data.get("training_summary"):
                    st.success(output_data["training_summary"])

                # Show if this is real ML training
                if output_data.get("real_ml_training"):
                    st.info("🎯 **Real Machine Learning Models Trained** using sklearn/XGBoost libraries!")

                # Training details
                if output_data.get("training_details"):
                    details = output_data["training_details"]
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Dataset Size", f"{details.get('dataset_shape', 'Unknown')}")
                        st.metric("Features Used", details.get('features_used', 'Unknown'))

                    with col2:
                        st.metric("Problem Type", details.get('problem_type', 'Unknown').title())
                        st.metric("Target Column", details.get('target_column', 'Unknown'))

                    with col3:
                        st.metric("Training Time", details.get('total_training_time', 'Unknown'))
                        st.metric("Libraries", details.get('libraries_used', 'sklearn'))

                # Model results
                if output_data.get("models_trained"):
                    st.subheader("🏆 Trained Models")

                    for i, model in enumerate(output_data["models_trained"]):
                        with st.expander(f"#{i+1} {model.get('model_name', 'Unknown Model')} - Score: {model.get('performance_score', 0):.3f}"):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.write(f"**Training Time:** {model.get('training_time', 0):.2f} seconds")
                                if model.get('metrics'):
                                    st.write("**Performance Metrics:**")
                                    for metric, value in model['metrics'].items():
                                        if isinstance(value, (int, float)):
                                            st.write(f"• {metric}: {value:.4f}")

                            with col2:
                                # Model download button
                                if model.get('model_path') and os.path.exists(model['model_path']):
                                    with open(model['model_path'], 'rb') as f:
                                        model_data = f.read()

                                    st.download_button(
                                        label=f"📥 Download {model['model_name']} Model",
                                        data=model_data,
                                        file_name=os.path.basename(model['model_path']),
                                        mime="application/octet-stream",
                                        key=f"download_model_{i}_{step_index}"
                                    )

                                    st.write(f"**Model Path:** `{model['model_path']}`")
                                else:
                                    st.warning("Model file not found for download")

                # Best model highlight
                if output_data.get("best_model"):
                    best = output_data["best_model"]
                    st.success(f"🥇 **Best Model:** {best.get('model_name', 'Unknown')} with {best.get('performance_score', 0):.1%} performance")

                # Feature names
                if output_data.get("feature_names"):
                    with st.expander("📊 Features Used in Training"):
                        st.write(", ".join(output_data["feature_names"]))

                # Model paths for download
                if output_data.get("model_paths"):
                    with st.expander("📁 All Model Files"):
                        for path in output_data["model_paths"]:
                            if os.path.exists(path):
                                st.write(f"✅ {os.path.basename(path)}")
                            else:
                                st.write(f"❌ {os.path.basename(path)} (not found)")

                # Legacy metrics display for backward compatibility
                if output_data.get("metrics"):
                    st.write("**Additional Metrics:**")
                    for metric, value in output_data["metrics"].items():
                        if isinstance(value, (int, float)):
                            st.write(f"- {metric}: {value:.4f}")

            else:
                # Generic display
                if isinstance(output_data, dict) and len(output_data) < 10:
                    for key, value in output_data.items():
                        if isinstance(value, (int, float)):
                            st.metric(key.replace('_', ' ').title(), f"{value:.4f}" if isinstance(value, float) else str(value))
                        elif isinstance(value, str) and len(value) < 200:
                            st.write(f"**{key.replace('_', ' ').title()}:** {value}")
                        else:
                            with st.expander(f"📋 {key.replace('_', ' ').title()}"):
                                if isinstance(value, (list, dict)):
                                    st.json(value)
                                else:
                                    st.write(value)
                else:
                    st.json(output_data)

        # User feedback for completed or waiting steps
        if status in ["completed", "waiting_approval"]:
            st.write("**Your Decision:**")
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button(f"✅ Approve", key=f"approve_{step_index}"):
                    submit_feedback(step_index, "approve", "")

            with col2:
                if st.button(f"❌ Reject", key=f"reject_{step_index}"):
                    st.session_state[f"show_reject_{step_index}"] = True
                    st.rerun()

            with col3:
                if st.button(f"✏️ Modify", key=f"modify_{step_index}"):
                    st.session_state[f"show_modify_{step_index}"] = True
                    st.rerun()

            # Show feedback forms
            if st.session_state.get(f"show_reject_{step_index}", False):
                with st.form(f"reject_form_{step_index}"):
                    feedback = st.text_area("Why are you rejecting this step?", key=f"reject_feedback_{step_index}")
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("Submit Rejection"):
                            submit_feedback(step_index, "reject", feedback)
                            st.session_state[f"show_reject_{step_index}"] = False
                    with col2:
                        if st.form_submit_button("Cancel"):
                            st.session_state[f"show_reject_{step_index}"] = False
                            st.rerun()

            if st.session_state.get(f"show_modify_{step_index}", False):
                with st.form(f"modify_form_{step_index}"):
                    feedback = st.text_area("How would you like to modify this step?", key=f"modify_feedback_{step_index}")
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("Submit Modification"):
                            submit_feedback(step_index, "modify", feedback)
                            st.session_state[f"show_modify_{step_index}"] = False
                    with col2:
                        if st.form_submit_button("Cancel"):
                            st.session_state[f"show_modify_{step_index}"] = False
                            st.rerun()

        # Show alternatives if step was rejected
        if output_data.get("show_alternatives", False):
            st.warning("❌ **Initial recommendations rejected.** Choose an alternative or use chat:")

            alternatives = output_data.get("alternatives", [])
            if alternatives:
                st.subheader("🔄 Alternative Approaches")

                for i, alt in enumerate(alternatives):
                    with st.expander(f"Alternative {i+1}: {alt.get('title', 'Option')}"):
                        st.write(alt.get('description', 'No description available'))

                        if st.button(f"Use This Alternative", key=f"alt_{step_index}_{i}"):
                            # Implement the alternative
                            st.info(f"Implementing alternative: {alt.get('title')}")
                            # Here you would call the API to implement this alternative
                            st.rerun()

        # Show context-aware chat if enabled
        if output_data.get("chat_enabled", False):
            st.subheader("💬 AI Assistant - Context Aware")
            st.info(f"🧠 **I understand you're working on: {step_name}**\nI have full context of your pipeline and can implement your requests.")

            # Chat interface
            chat_input = st.text_area(
                "Tell me exactly what you want to change or ask me anything:",
                placeholder="Examples:\n- Set learning_rate to 0.1\n- What parameters were used for training?\n- Use a different algorithm\n- Explain the current results",
                key=f"chat_input_{step_index}",
                height=100
            )

            col1, col2 = st.columns([1, 4])
            with col1:
                if st.button("💬 Send", key=f"chat_send_{step_index}"):
                    if chat_input.strip():
                        with st.spinner("🤖 AI is processing your request..."):
                            # Send chat request with full context
                            chat_response = make_api_request("/chat", method="POST", data={
                                "message": chat_input,
                                "pipeline_id": st.session_state.pipeline_id,
                                "context": {
                                    "current_step": step_name,
                                    "step_data": output_data,
                                    "user_request": chat_input
                                }
                            })

                            if "error" not in chat_response:
                                st.success("✅ **AI Response:**")
                                st.write(chat_response.get("response", "No response"))

                                # Show if action was executed
                                if chat_response.get("action_executed", False):
                                    st.success(f"🔧 **Action Executed:** {chat_response.get('action_type', 'Unknown')}")

                                    # Refresh the pipeline to show updates
                                    time.sleep(2)
                                    st.rerun()

                                # Show suggestions
                                suggestions = chat_response.get("suggestions", [])
                                if suggestions:
                                    st.write("**Suggestions:**")
                                    for suggestion in suggestions:
                                        st.write(f"• {suggestion}")
                            else:
                                st.error(f"Chat error: {chat_response['error']}")
                    else:
                        st.warning("Please enter a message")

            with col2:
                st.write("**Quick Actions:**")
                quick_actions = [
                    "What parameters were used?",
                    "Set learning_rate to 0.1",
                    "Use a different algorithm",
                    "Retrain the models",
                    "Explain the results"
                ]

                for action in quick_actions:
                    if st.button(action, key=f"quick_{step_index}_{action}"):
                        # Auto-fill the chat input
                        st.session_state[f"chat_input_{step_index}"] = action
                        st.rerun()

        # Show error if step failed
        if step.get("error_message"):
            st.error(f"**Error:** {step['error_message']}")

def submit_feedback(step_index: int, action: str, feedback: str):
    """Submit user feedback for a pipeline step - PRODUCTION VERSION"""
    feedback_data = {
        "step_index": step_index,
        "action": action,
        "feedback": feedback
    }

    result = make_api_request(f"/pipeline/{st.session_state.pipeline_id}/feedback", method="POST", data=feedback_data)

    if "error" not in result:
        if action == "approve":
            st.success("✅ Step approved! Proceeding to next step...")
        elif action == "reject":
            # Check if alternatives were provided
            if result.get("refresh_current_step", False):
                st.warning("❌ Step rejected. Alternatives and chat are now available below.")
            else:
                st.warning("❌ Step rejected. AI will provide alternatives.")
        elif action == "modify":
            # Check if chat was enabled
            if result.get("refresh_current_step", False):
                st.info("✏️ Chat enabled! Use the AI assistant below to specify your exact requirements.")
            else:
                st.info("✏️ Modification request submitted.")

        # Automatically refresh pipeline status after feedback
        time.sleep(1)  # Reduced wait time for better UX
        with st.spinner("Updating step..."):
            status_result = make_api_request(f"/pipeline/{st.session_state.pipeline_id}/status")
            if "error" not in status_result:
                st.session_state.pipeline_steps = status_result.get("steps", [])

                # Show specific success message based on action
                if action == "approve":
                    st.success("✅ Pipeline updated! Moving to next step...")
                elif action in ["reject", "modify"]:
                    st.success("🔄 Step updated with alternatives/chat enabled!")
            else:
                st.warning("Could not refresh pipeline status automatically. Please click 'Refresh Status'.")

        st.rerun()
    else:
        st.error(f"Failed to submit feedback: {result['error']}")

def reset_session():
    """Reset session state for new pipeline"""
    keys_to_reset = [
        'step', 'pipeline_id', 'user_query', 'data_source_type', 'selected_dataset',
        'available_datasets', 'pipeline_steps', 'db_connection', 'uploaded_file_path'
    ]

    for key in keys_to_reset:
        if key in st.session_state:
            if key == 'step':
                st.session_state[key] = 'data_source'
            elif key in ['available_datasets', 'pipeline_steps']:
                st.session_state[key] = []
            else:
                st.session_state[key] = None

# Main application
def main():
    """Main application function"""
    # PRODUCTION VERSION - NO SIDEBAR FOR STREAMLINED UI

    # Route to appropriate step
    if st.session_state.step == 'data_source':
        step_1_data_source()
    elif st.session_state.step == 'database_setup':
        step_2_database_setup()
    elif st.session_state.step == 'file_upload':
        step_2_file_upload()
    elif st.session_state.step == 'dataset_selection':
        step_3_dataset_selection()
    elif st.session_state.step == 'query_input':
        step_4_query_input()
    elif st.session_state.step == 'pipeline_execution':
        step_5_pipeline_execution()
    else:
        st.error("Unknown step. Resetting to start.")
        st.session_state.step = 'data_source'
        st.rerun()

if __name__ == "__main__":
    main()