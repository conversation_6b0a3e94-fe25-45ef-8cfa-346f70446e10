"""
Streamlit UI for AI Data Science Pipeline
Interactive web interface for the data science workflow
"""
import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
import json
import sys
import os
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings

# Configure Streamlit page
st.set_page_config(
    page_title="AI Data Science Pipeline",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API endpoints
ORCHESTRATOR_BASE_URL = f"http://{settings.API_HOST}:{settings.API_PORT}"