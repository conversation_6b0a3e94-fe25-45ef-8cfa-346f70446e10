"""
Streamlit UI for AI Data Science Pipeline
Simple, integrated interface for the data science workflow
"""
import streamlit as st
import requests
import pandas as pd
import json
import sys
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings

# Configure Streamlit page
st.set_page_config(
    page_title="AI Data Science Pipeline",
    page_icon="🤖",
    layout="wide"
)

# API endpoints
API_BASE_URL = f"http://{settings.API_HOST}:{settings.API_PORT}"

# Initialize session state
if 'step' not in st.session_state:
    st.session_state.step = 'data_source'
if 'pipeline_id' not in st.session_state:
    st.session_state.pipeline_id = None
if 'user_query' not in st.session_state:
    st.session_state.user_query = ""
if 'data_source_type' not in st.session_state:
    st.session_state.data_source_type = None
if 'selected_dataset' not in st.session_state:
    st.session_state.selected_dataset = None
if 'available_datasets' not in st.session_state:
    st.session_state.available_datasets = []
if 'pipeline_steps' not in st.session_state:
    st.session_state.pipeline_steps = []
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'db_connection' not in st.session_state:
    st.session_state.db_connection = None
if 'uploaded_file_path' not in st.session_state:
    st.session_state.uploaded_file_path = None

# Simple CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f77b4, #ff7f0e);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    .step-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    .chat-msg {
        padding: 0.5rem;
        margin: 0.5rem 0;
        border-radius: 0.3rem;
    }
    .user-msg {
        background-color: #e3f2fd;
        text-align: right;
    }
    .ai-msg {
        background-color: #f5f5f5;
    }
    .dataset-card {
        border: 1px solid #ddd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .recommended {
        border-color: #28a745;
        background-color: #d4edda;
    }
</style>
""", unsafe_allow_html=True)

# Utility functions
def make_api_request(endpoint: str, method: str = "GET", data: Dict = None, files: Dict = None) -> Dict[str, Any]:
    """Make API request to the orchestrator"""
    url = f"{API_BASE_URL}{endpoint}"
    try:
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            if files:
                response = requests.post(url, files=files, timeout=60)
            else:
                response = requests.post(url, json=data, timeout=60)

        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def display_chat_sidebar():
    """Display simple chat interface in sidebar"""
    with st.sidebar:
        st.subheader("💬 AI Assistant")

        # Current context
        if st.session_state.step:
            st.info(f"Current Step: {st.session_state.step.replace('_', ' ').title()}")

        # Chat history (last 3 messages)
        if st.session_state.chat_history:
            st.write("**Recent Chat:**")
            for msg in st.session_state.chat_history[-3:]:
                if msg["type"] == "user":
                    st.markdown(f'<div class="chat-msg user-msg">You: {msg["content"]}</div>', unsafe_allow_html=True)
                else:
                    st.markdown(f'<div class="chat-msg ai-msg">AI: {msg["content"]}</div>', unsafe_allow_html=True)

        # Chat input
        user_message = st.text_input("Ask AI:", placeholder="How can I help?")
        if st.button("Send") and user_message.strip():
            # Add user message
            st.session_state.chat_history.append({
                "type": "user",
                "content": user_message,
                "timestamp": datetime.now()
            })

            # Get AI response
            chat_data = {
                "message": user_message,
                "pipeline_id": st.session_state.pipeline_id or "",
                "context": {
                    "current_step": st.session_state.step,
                    "user_query": st.session_state.user_query,
                    "data_source": st.session_state.data_source_type
                }
            }

            result = make_api_request("/chat", method="POST", data=chat_data)

            if "error" not in result:
                ai_response = result.get("response", "I couldn't process your request.")
                st.session_state.chat_history.append({
                    "type": "ai",
                    "content": ai_response,
                    "timestamp": datetime.now()
                })
            else:
                st.session_state.chat_history.append({
                    "type": "ai",
                    "content": "I'm having technical difficulties. Please try again.",
                    "timestamp": datetime.now()
                })

            st.rerun()

def step_1_data_source():
    """Step 1: Choose data source using dropdown"""
    st.markdown("""
    <div class="main-header">
        <h1>🤖 AI Data Science Pipeline</h1>
        <p>Choose your data source to begin</p>
    </div>
    """, unsafe_allow_html=True)

    st.markdown("""
    <div class="step-card">
        <h3>Step 1: Select Data Source</h3>
        <p>Choose how you want to provide your data for analysis</p>
    </div>
    """, unsafe_allow_html=True)

    # Dropdown for data source selection
    data_source_option = st.selectbox(
        "Select your data source:",
        ["-- Select an option --", "Database (PostgreSQL/MongoDB)", "Upload File (CSV/Excel/JSON)"],
        key="data_source_dropdown"
    )

    if data_source_option == "Database (PostgreSQL/MongoDB)":
        st.session_state.data_source_type = "database"
        if st.button("Continue with Database", type="primary"):
            st.session_state.step = "database_setup"
            st.rerun()

    elif data_source_option == "Upload File (CSV/Excel/JSON)":
        st.session_state.data_source_type = "file"
        if st.button("Continue with File Upload", type="primary"):
            st.session_state.step = "file_upload"
            st.rerun()

def step_2_database_setup():
    """Step 2: Database connection and dataset discovery"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 2: Database Connection</h3>
        <p>Connect to your database and discover available datasets</p>
    </div>
    """, unsafe_allow_html=True)

    # Database type selection
    db_type = st.selectbox("Database Type:", ["PostgreSQL", "MongoDB"])

    # Connection form
    with st.form("db_connection"):
        col1, col2 = st.columns(2)
        with col1:
            host = st.text_input("Host", value="localhost")
            database = st.text_input("Database Name")
        with col2:
            port = st.number_input("Port", value=5432 if db_type == "PostgreSQL" else 27017)
            username = st.text_input("Username")

        password = st.text_input("Password", type="password")

        if st.form_submit_button("Connect and Discover Datasets", type="primary"):
            if not all([host, database, username, password]):
                st.error("Please fill in all connection details.")
                return

            with st.spinner("Connecting to database and discovering datasets..."):
                # Store connection details
                st.session_state.db_connection = {
                    "type": db_type,
                    "host": host,
                    "port": port,
                    "database": database,
                    "username": username,
                    "password": password
                }

                # Call MCP server to discover datasets
                discovery_data = {
                    "db_type": db_type,
                    "connection_details": {
                        "host": host,
                        "port": port,
                        "database": database,
                        "username": username,
                        "password": password
                    }
                }

                result = make_api_request("/discover_datasets", method="POST", data=discovery_data)

                if "error" not in result:
                    datasets = result.get("datasets", [])
                    st.session_state.available_datasets = datasets
                    st.success(f"✅ Connected successfully! Found {len(datasets)} datasets.")
                else:
                    # Fallback to mock data for demo purposes
                    mock_datasets = [
                        {
                            "name": "customers",
                            "description": "Customer information and demographics",
                            "rows": 10000,
                            "columns": ["customer_id", "name", "age", "gender", "city", "signup_date", "total_purchases"]
                        },
                        {
                            "name": "orders",
                            "description": "Order transactions and details",
                            "rows": 50000,
                            "columns": ["order_id", "customer_id", "product_id", "quantity", "price", "order_date", "status"]
                        },
                        {
                            "name": "products",
                            "description": "Product catalog and information",
                            "rows": 1000,
                            "columns": ["product_id", "name", "category", "price", "stock", "rating", "description"]
                        },
                        {
                            "name": "user_activity",
                            "description": "User website activity and behavior",
                            "rows": 75000,
                            "columns": ["user_id", "session_id", "page_views", "time_spent", "clicks", "bounce_rate"]
                        }
                    ]

                    st.session_state.available_datasets = mock_datasets
                    st.warning(f"⚠️ Using demo datasets. Database connection failed: {result.get('error', 'Unknown error')}")
                    st.success(f"📊 Loaded {len(mock_datasets)} demo datasets for testing.")
                time.sleep(1)
                st.session_state.step = "dataset_selection"
                st.rerun()

    if st.button("← Back to Data Source"):
        st.session_state.step = "data_source"
        st.rerun()

def step_2_file_upload():
    """Step 2: File upload"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 2: Upload Your Dataset</h3>
        <p>Upload your data file for analysis</p>
    </div>
    """, unsafe_allow_html=True)

    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['csv', 'xlsx', 'xls', 'json'],
        help="Supported formats: CSV, Excel, JSON"
    )

    if uploaded_file is not None:
        # Display file info
        st.write("**File Information:**")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Filename", uploaded_file.name)
        with col2:
            st.metric("Size", f"{uploaded_file.size / 1024:.1f} KB")
        with col3:
            st.metric("Type", uploaded_file.type)

        # File preview
        try:
            if uploaded_file.name.endswith('.csv'):
                df_preview = pd.read_csv(uploaded_file, nrows=5)
                st.write("**Preview (first 5 rows):**")
                st.dataframe(df_preview)
                uploaded_file.seek(0)  # Reset file pointer
        except Exception as e:
            st.warning(f"Could not preview file: {str(e)}")

        # Upload button
        if st.button("Upload and Continue", type="primary"):
            with st.spinner("Uploading file..."):
                files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}
                result = make_api_request("/upload_file", method="POST", files=files)

                if "error" not in result:
                    st.success("✅ File uploaded successfully!")
                    st.session_state.uploaded_file_path = result.get("file_path")
                    st.session_state.selected_dataset = {
                        "name": uploaded_file.name,
                        "path": result.get("file_path"),
                        "type": "uploaded_file"
                    }
                    time.sleep(1)
                    st.session_state.step = "query_input"
                    st.rerun()
                else:
                    st.error(f"❌ Upload failed: {result['error']}")

    if st.button("← Back to Data Source"):
        st.session_state.step = "data_source"
        st.rerun()

def step_3_dataset_selection():
    """Step 3: Display datasets and get AI recommendations"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 3: Dataset Selection</h3>
        <p>Review available datasets and get AI recommendations</p>
    </div>
    """, unsafe_allow_html=True)

    # Show available datasets
    st.subheader("Available Datasets")

    for i, dataset in enumerate(st.session_state.available_datasets):
        with st.expander(f"📊 {dataset['name']} ({dataset['rows']:,} rows)"):
            st.write(f"**Description:** {dataset['description']}")
            st.write(f"**Columns:** {', '.join(dataset['columns'])}")

            if st.button(f"Select {dataset['name']}", key=f"select_{i}"):
                st.session_state.selected_dataset = dataset
                st.session_state.step = "query_input"
                st.rerun()

    # Query input for recommendations
    st.subheader("Get AI Dataset Recommendation")
    user_query = st.text_area(
        "Describe what you want to achieve:",
        placeholder="e.g., I want to predict customer churn based on their behavior...",
        height=100
    )

    if st.button("Get AI Recommendation", type="primary") and user_query.strip():
        st.session_state.user_query = user_query

        with st.spinner("AI is analyzing your requirements and recommending datasets..."):
            # Call MCP server for dataset recommendation
            recommendation_data = {
                "user_query": user_query,
                "available_datasets": st.session_state.available_datasets
            }

            result = make_api_request("/suggest_datasets", method="POST", data=recommendation_data)

            if "error" not in result:
                recommended_datasets = result.get("recommended_datasets", [])

                st.subheader("🤖 AI Recommendations")
                for i, rec in enumerate(recommended_datasets):
                    dataset_name = rec.get("dataset_name", "Unknown")
                    confidence = rec.get("confidence", 0)
                    reasoning = rec.get("reasoning", "No reasoning provided")

                    # Find the full dataset info
                    dataset_info = next((ds for ds in st.session_state.available_datasets if ds["name"] == dataset_name), None)

                    if dataset_info:
                        card_class = "dataset-card recommended" if i == 0 else "dataset-card"
                        st.markdown(f"""
                        <div class="{card_class}">
                            <h4>{'🥇 ' if i == 0 else ''}Recommendation {i+1}: {dataset_name}</h4>
                            <p><strong>Confidence:</strong> {confidence:.1%}</p>
                            <p><strong>Reasoning:</strong> {reasoning}</p>
                            <p><strong>Columns:</strong> {', '.join(dataset_info['columns'])}</p>
                        </div>
                        """, unsafe_allow_html=True)

                        if st.button(f"Select {dataset_name}", key=f"rec_select_{i}", type="primary" if i == 0 else "secondary"):
                            st.session_state.selected_dataset = dataset_info
                            st.session_state.step = "query_input"
                            st.rerun()
            else:
                st.error(f"Failed to get recommendations: {result['error']}")

    if st.button("← Back to Database Setup"):
        st.session_state.step = "database_setup"
        st.rerun()

def step_4_query_input():
    """Step 4: User query input and pipeline start"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 4: Define Your Analysis Goal</h3>
        <p>Describe what you want to achieve with your data</p>
    </div>
    """, unsafe_allow_html=True)

    # Show selected dataset
    if st.session_state.selected_dataset:
        dataset = st.session_state.selected_dataset
        st.info(f"**Selected Dataset:** {dataset['name']}")

        if dataset.get("columns"):
            st.write(f"**Available Columns:** {', '.join(dataset['columns'])}")

    # User query input
    user_query = st.text_area(
        "Describe your analysis goal:",
        value=st.session_state.user_query,
        placeholder="Example: I want to predict which customers will churn based on their purchase history and demographics...",
        height=120
    )

    # Optional target column
    target_column = st.text_input(
        "Target Column (optional):",
        help="If you know which column contains the values you want to predict"
    )

    if st.button("Start AI Pipeline", type="primary") and user_query.strip():
        st.session_state.user_query = user_query

        with st.spinner("Starting AI pipeline..."):
            # Prepare pipeline data
            pipeline_data = {
                "user_request": user_query,
                "target_column": target_column if target_column else None
            }

            # Add dataset path based on source type
            if st.session_state.data_source_type == "file":
                pipeline_data["dataset_path"] = st.session_state.uploaded_file_path
            else:
                # For database, we'll use the selected dataset info
                pipeline_data["dataset_info"] = st.session_state.selected_dataset
                pipeline_data["db_connection"] = st.session_state.db_connection

            result = make_api_request("/start_pipeline", method="POST", data=pipeline_data)

            if "error" not in result:
                st.session_state.pipeline_id = result.get("pipeline_id")
                st.success("✅ Pipeline started successfully!")
                time.sleep(1)
                st.session_state.step = "pipeline_execution"
                st.rerun()
            else:
                st.error(f"❌ Failed to start pipeline: {result['error']}")

    # Back button
    if st.button("← Back"):
        if st.session_state.data_source_type == "database":
            st.session_state.step = "dataset_selection"
        else:
            st.session_state.step = "file_upload"
        st.rerun()

def step_5_pipeline_execution():
    """Step 5: Pipeline execution with step-by-step approval"""
    st.markdown("""
    <div class="step-card">
        <h3>Step 5: AI Pipeline Execution</h3>
        <p>Watch AI process your data step by step</p>
    </div>
    """, unsafe_allow_html=True)

    # Pipeline info
    col1, col2 = st.columns(2)
    with col1:
        st.metric("Pipeline ID", st.session_state.pipeline_id[:8] + "..." if st.session_state.pipeline_id else "N/A")
    with col2:
        st.metric("Goal", st.session_state.user_query[:30] + "..." if len(st.session_state.user_query) > 30 else st.session_state.user_query)

    # Auto-refresh option
    auto_refresh = st.checkbox("Auto-refresh every 5 seconds")

    # Get pipeline status
    if st.button("🔄 Refresh Status") or auto_refresh or not st.session_state.pipeline_steps:
        with st.spinner("Getting pipeline status..."):
            result = make_api_request(f"/pipeline/{st.session_state.pipeline_id}/status")

            if "error" not in result:
                st.session_state.pipeline_steps = result.get("steps", [])
            else:
                st.error(f"Failed to get pipeline status: {result['error']}")
                return

    # Display pipeline steps
    if st.session_state.pipeline_steps:
        st.subheader("📋 Pipeline Steps")

        for i, step in enumerate(st.session_state.pipeline_steps):
            display_pipeline_step(i, step)
    else:
        st.info("Pipeline is initializing... Please wait and refresh.")

    # Auto-refresh logic
    if auto_refresh:
        time.sleep(5)
        st.rerun()

    # Reset button
    if st.button("🔄 Start New Pipeline"):
        reset_session()
        st.rerun()

def display_pipeline_step(step_index: int, step: Dict):
    """Display individual pipeline step with approval options"""
    step_name = step.get("step_name", "Unknown").replace("_", " ").title()
    status = step.get("status", "pending")

    # Status icons
    status_icons = {
        "completed": "✅",
        "running": "🔄",
        "failed": "❌",
        "pending": "⏳",
        "waiting_approval": "⏸️"
    }

    icon = status_icons.get(status, "❓")

    with st.expander(f"{icon} Step {step_index + 1}: {step_name} ({status.title()})", expanded=(status in ["waiting_approval", "failed"])):
        # Step details
        if step.get("started_at"):
            st.write(f"**Started:** {step['started_at']}")
        if step.get("completed_at"):
            st.write(f"**Completed:** {step['completed_at']}")
        if step.get("execution_time"):
            st.write(f"**Duration:** {step['execution_time']:.2f} seconds")

        # Show step results
        if step.get("output_data"):
            st.write("**Results:**")
            output_data = step["output_data"]

            # Display results based on step type
            if "problem_type" in step_name.lower():
                st.info(f"**Detected Problem Type:** {output_data.get('detected_problem_type', 'Unknown')}")
                st.write(f"**Confidence:** {output_data.get('confidence_score', 0):.2%}")
                st.write(f"**Reasoning:** {output_data.get('reasoning', 'No reasoning provided')}")

            elif "train" in step_name.lower():
                if output_data.get("training_summary"):
                    st.success(output_data["training_summary"])
                if output_data.get("metrics"):
                    st.write("**Metrics:**")
                    for metric, value in output_data["metrics"].items():
                        st.write(f"- {metric}: {value:.4f}")

            else:
                # Generic display
                st.json(output_data)

        # User feedback for completed or waiting steps
        if status in ["completed", "waiting_approval"]:
            st.write("**Your Decision:**")
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button(f"✅ Approve", key=f"approve_{step_index}"):
                    submit_feedback(step_index, "approve", "")

            with col2:
                if st.button(f"❌ Reject", key=f"reject_{step_index}"):
                    st.session_state[f"show_reject_{step_index}"] = True
                    st.rerun()

            with col3:
                if st.button(f"✏️ Modify", key=f"modify_{step_index}"):
                    st.session_state[f"show_modify_{step_index}"] = True
                    st.rerun()

            # Show feedback forms
            if st.session_state.get(f"show_reject_{step_index}", False):
                with st.form(f"reject_form_{step_index}"):
                    feedback = st.text_area("Why are you rejecting this step?", key=f"reject_feedback_{step_index}")
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("Submit Rejection"):
                            submit_feedback(step_index, "reject", feedback)
                            st.session_state[f"show_reject_{step_index}"] = False
                    with col2:
                        if st.form_submit_button("Cancel"):
                            st.session_state[f"show_reject_{step_index}"] = False
                            st.rerun()

            if st.session_state.get(f"show_modify_{step_index}", False):
                with st.form(f"modify_form_{step_index}"):
                    feedback = st.text_area("How would you like to modify this step?", key=f"modify_feedback_{step_index}")
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("Submit Modification"):
                            submit_feedback(step_index, "modify", feedback)
                            st.session_state[f"show_modify_{step_index}"] = False
                    with col2:
                        if st.form_submit_button("Cancel"):
                            st.session_state[f"show_modify_{step_index}"] = False
                            st.rerun()

        # Show error if step failed
        if step.get("error_message"):
            st.error(f"**Error:** {step['error_message']}")

def submit_feedback(step_index: int, action: str, feedback: str):
    """Submit user feedback for a pipeline step"""
    feedback_data = {
        "step_index": step_index,
        "action": action,
        "feedback": feedback
    }

    result = make_api_request(f"/pipeline/{st.session_state.pipeline_id}/feedback", method="POST", data=feedback_data)

    if "error" not in result:
        if action == "approve":
            st.success("✅ Step approved!")
        elif action == "reject":
            st.warning("❌ Step rejected. AI will provide alternatives.")
        elif action == "modify":
            st.info("✏️ Modification request submitted.")

        # Automatically refresh pipeline status after feedback
        time.sleep(2)  # Give the backend time to process
        with st.spinner("Updating pipeline status..."):
            status_result = make_api_request(f"/pipeline/{st.session_state.pipeline_id}/status")
            if "error" not in status_result:
                st.session_state.pipeline_steps = status_result.get("steps", [])
                st.success("Pipeline status updated!")
            else:
                st.warning("Could not refresh pipeline status automatically. Please click 'Refresh Status'.")

        st.rerun()
    else:
        st.error(f"Failed to submit feedback: {result['error']}")

def reset_session():
    """Reset session state for new pipeline"""
    keys_to_reset = [
        'step', 'pipeline_id', 'user_query', 'data_source_type', 'selected_dataset',
        'available_datasets', 'pipeline_steps', 'db_connection', 'uploaded_file_path'
    ]

    for key in keys_to_reset:
        if key in st.session_state:
            if key == 'step':
                st.session_state[key] = 'data_source'
            elif key in ['available_datasets', 'pipeline_steps']:
                st.session_state[key] = []
            else:
                st.session_state[key] = None

# Main application
def main():
    """Main application function"""
    # Display chat sidebar
    display_chat_sidebar()

    # Route to appropriate step
    if st.session_state.step == 'data_source':
        step_1_data_source()
    elif st.session_state.step == 'database_setup':
        step_2_database_setup()
    elif st.session_state.step == 'file_upload':
        step_2_file_upload()
    elif st.session_state.step == 'dataset_selection':
        step_3_dataset_selection()
    elif st.session_state.step == 'query_input':
        step_4_query_input()
    elif st.session_state.step == 'pipeline_execution':
        step_5_pipeline_execution()
    else:
        st.error("Unknown step. Resetting to start.")
        st.session_state.step = 'data_source'
        st.rerun()

if __name__ == "__main__":
    main()