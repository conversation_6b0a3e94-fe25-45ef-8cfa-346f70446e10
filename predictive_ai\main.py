"""
Main entry point for the AI Data Science Pipeline
"""
import asyncio
import uvicorn
import subprocess
import sys
import os
from multiprocessing import Process
from loguru import logger

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings


def start_celery_worker():
    """Start Celery worker process"""
    logger.info("Starting Celery worker...")
    cmd = [
        sys.executable, "-m", "celery","-A",
        "mcp_server.celery:celery_app",
        "worker",
        "--loglevel=info",
        f"--concurrency={settings.CELERY_CONCURRENCY}",
        "--pool=solo" if os.name == 'nt' else "--pool=prefork"  # Use solo pool on Windows
    ]
    subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))


def start_orchestrator():
    """Start FastAPI orchestrator"""
    logger.info("Starting FastAPI orchestrator...")
    from client_agent.orchestrator import app

    uvicorn.run(
        app,
        host=settings.API_HOST,
        port=settings.API_PORT,
        log_level="info"
    )


def start_streamlit():
    """Start Streamlit UI"""
    logger.info("Starting Streamlit UI...")
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "ui/streamlit_ui.py",
        "--server.port", str(settings.API_PORT + 1),
        "--server.address", settings.API_HOST
    ]
    subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))


def main():
    """Main function to start all services"""
    logger.info("🚀 Starting AI Data Science Pipeline...")

    # Check if Redis is available for Celery
    try:
        import redis
        r = redis.Redis.from_url(settings.REDIS_URL)
        r.ping()
        logger.info("✅ Redis connection successful")
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        logger.error("Please ensure Redis is running for Celery task queue")
        return

    # Start services in separate processes
    processes = []

    try:
        # Start Celery worker
        celery_process = Process(target=start_celery_worker)
        celery_process.start()
        processes.append(celery_process)
        logger.info("✅ Celery worker started")

        # Start FastAPI orchestrator
        orchestrator_process = Process(target=start_orchestrator)
        orchestrator_process.start()
        processes.append(orchestrator_process)
        logger.info("✅ FastAPI orchestrator started")

        # Start Streamlit UI
        streamlit_process = Process(target=start_streamlit)
        streamlit_process.start()
        processes.append(streamlit_process)
        logger.info("✅ Streamlit UI started")

        logger.info("🎉 All services started successfully!")
        logger.info(f"📊 Streamlit UI: http://{settings.API_HOST}:{settings.API_PORT + 1}")
        logger.info(f"🔧 API Orchestrator: http://{settings.API_HOST}:{settings.API_PORT}")
        logger.info("Press Ctrl+C to stop all services")

        # Wait for all processes
        for process in processes:
            process.join()

    except KeyboardInterrupt:
        logger.info("🛑 Shutting down services...")
        for process in processes:
            process.terminate()
            process.join()
        logger.info("✅ All services stopped")

    except Exception as e:
        logger.error(f"❌ Error starting services: {e}")
        for process in processes:
            if process.is_alive():
                process.terminate()
                process.join()


def start_dev_mode():
    """Start in development mode (individual components)"""
    import argparse

    parser = argparse.ArgumentParser(description="AI Data Science Pipeline")
    parser.add_argument("--component", choices=["celery", "api", "ui", "all"],
                       default="all", help="Component to start")

    args = parser.parse_args()

    if args.component == "celery":
        start_celery_worker()
    elif args.component == "api":
        start_orchestrator()
    elif args.component == "ui":
        start_streamlit()
    else:
        main()


if __name__ == "__main__":
    # Check if running in development mode
    if len(sys.argv) > 1:
        start_dev_mode()
    else:
        main()