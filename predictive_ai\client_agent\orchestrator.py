"""
Orchestrator for AI Data Science Pipeline
Manages the workflow and coordinates between different tools
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import sys
import os
from loguru import logger

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import (
    PipelineState, PipelineStep, TaskStatus, ProblemType,
    UserFeedback, ChatRequest, ChatResponse
)

# Initialize FastAPI app
app = FastAPI(
    title="AI Data Science Pipeline Orchestrator",
    description="Orchestrates the AI-powered data science workflow",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for pipeline states (in production, use Redis or database)
pipeline_states: Dict[str, PipelineState] = {}

class PipelineOrchestrator:
    """Main orchestrator class for managing the data science pipeline"""

    def __init__(self):
        self.mcp_client = None  # Will be initialized when needed

    async def start_pipeline(self, user_request: str, dataset_path: Optional[str] = None,
                           dataset_info: Optional[Dict] = None, db_connection: Optional[Dict] = None,
                           target_column: Optional[str] = None) -> str:
        """Start a new data science pipeline"""
        pipeline_id = str(uuid.uuid4())

        # Determine starting step based on data source
        if dataset_path:
            # File uploaded - skip dataset suggestion and go to problem detection
            starting_step = "problem_detection"
            context = {
                "user_request": user_request,
                "dataset_path": dataset_path,
                "target_column": target_column,
                "data_source": "file"
            }
        elif dataset_info:
            # Dataset selected from database - skip to problem detection
            starting_step = "problem_detection"
            context = {
                "user_request": user_request,
                "dataset_info": dataset_info,
                "db_connection": db_connection,
                "target_column": target_column,
                "data_source": "database"
            }
        else:
            # No dataset provided - start with dataset suggestion
            starting_step = "dataset_suggestion"
            context = {
                "user_request": user_request,
                "data_source": "unknown"
            }

        # Create initial pipeline state
        pipeline_state = PipelineState(
            pipeline_id=pipeline_id,
            user_request=user_request,
            current_step=starting_step,
            steps=[],
            context=context
        )

        pipeline_states[pipeline_id] = pipeline_state

        logger.info(f"Started new pipeline: {pipeline_id} with starting step: {starting_step}")

        # Start with the appropriate first step
        await self.execute_step(pipeline_id, starting_step)

        return pipeline_id

    async def execute_step(self, pipeline_id: str, step_name: str, step_input: Optional[Dict] = None):
        """Execute a specific pipeline step"""
        if pipeline_id not in pipeline_states:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        pipeline_state = pipeline_states[pipeline_id]

        # Create step record
        step = PipelineStep(
            step_name=step_name,
            step_type=self._get_step_type(step_name),
            status=TaskStatus.RUNNING,
            input_data=step_input,
            started_at=datetime.now()
        )

        pipeline_state.steps.append(step)
        pipeline_state.current_step = step_name
        pipeline_state.updated_at = datetime.now()

        try:
            # Execute the step based on its name
            if step_name == "dataset_suggestion":
                result = await self._execute_dataset_suggestion(pipeline_state)
            elif step_name == "dataset_selection":
                result = await self._execute_dataset_selection(pipeline_state, step_input)
            elif step_name == "data_cleaning":
                result = await self._execute_data_cleaning(pipeline_state, step_input)
            elif step_name == "problem_detection":
                result = await self._execute_problem_detection(pipeline_state, step_input)
            elif step_name == "model_training":
                result = await self._execute_model_training(pipeline_state, step_input)
            elif step_name == "model_evaluation":
                result = await self._execute_model_evaluation(pipeline_state, step_input)
            elif step_name == "hyperparameter_tuning":
                result = await self._execute_hyperparameter_tuning(pipeline_state, step_input)
            else:
                raise ValueError(f"Unknown step: {step_name}")

            # Update step with success - set to waiting_approval for user review
            step.status = "waiting_approval"  # Changed from TaskStatus.SUCCESS
            step.output_data = result
            step.completed_at = datetime.now()
            step.execution_time = (step.completed_at - step.started_at).total_seconds()

            # Update pipeline context
            pipeline_state.context[f"{step_name}_result"] = result

            logger.info(f"Step {step_name} completed successfully for pipeline {pipeline_id} - waiting for user approval")

        except Exception as e:
            # Update step with failure
            step.status = TaskStatus.FAILED
            step.error_message = str(e)
            step.completed_at = datetime.now()
            step.execution_time = (step.completed_at - step.started_at).total_seconds()

            logger.error(f"Step {step_name} failed for pipeline {pipeline_id}: {e}")
            raise

    def _get_step_type(self, step_name: str) -> str:
        """Get the type of a pipeline step"""
        step_types = {
            "dataset_suggestion": "data_discovery",
            "dataset_selection": "data_discovery",
            "data_cleaning": "data_preprocessing",
            "problem_detection": "problem_analysis",
            "model_training": "model_development",
            "model_evaluation": "model_evaluation",
            "hyperparameter_tuning": "model_optimization"
        }
        return step_types.get(step_name, "unknown")

    async def _execute_dataset_suggestion(self, pipeline_state: PipelineState) -> Dict[str, Any]:
        """Execute dataset suggestion step"""
        from mcp_server.tools.suggest_datasets import suggest_datasets_task

        user_request = pipeline_state.user_request

        # Execute the task
        task_result = suggest_datasets_task.delay(user_request)
        result = task_result.get(timeout=60)

        return result

    async def _execute_dataset_selection(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute dataset selection step"""
        selected_dataset = step_input.get("selected_dataset")
        if not selected_dataset:
            raise ValueError("No dataset selected")

        # Store selected dataset in context
        return {
            "selected_dataset": selected_dataset,
            "dataset_path": selected_dataset.get("file_path") or selected_dataset.get("table_name")
        }

    async def _execute_data_cleaning(self, pipeline_state: PipelineState, step_input: Dict = None) -> Dict[str, Any]:
        """Execute LLM-powered comprehensive data cleaning and preprocessing step"""
        import os
        import pandas as pd
        import numpy as np
        from datetime import datetime

        # Get dataset path and user context
        dataset_path = None
        user_query = pipeline_state.user_request
        problem_detection_result = pipeline_state.context.get("problem_detection_result")
        problem_type = problem_detection_result.get("detected_problem_type", "regression") if problem_detection_result else "regression"

        if pipeline_state.context.get("dataset_path"):
            dataset_path = pipeline_state.context["dataset_path"]
        elif step_input and step_input.get("dataset_path"):
            dataset_path = step_input["dataset_path"]
        elif pipeline_state.context.get("dataset_selection_result"):
            dataset_path = pipeline_state.context["dataset_selection_result"].get("dataset_path")

        if not dataset_path:
            raise ValueError("No dataset path available for cleaning")

        try:
            # Initialize LLM Data Analyst
            from mcp_server.tools.llm_data_analyst import LLMDataAnalyst
            llm_analyst = LLMDataAnalyst()

            # Check LLM availability and log status
            available_models = llm_analyst.get_available_models()
            logger.info(f"Available LLM models: {available_models}")

            # Load the actual dataset for analysis
            if dataset_path.endswith('.csv'):
                df = pd.read_csv(dataset_path)
            else:
                # For demo purposes, create a mock dataset based on user query
                df = self._create_demo_dataset(user_query)

            original_shape = df.shape
            logger.info(f"Starting LLM-powered data cleaning for dataset shape: {original_shape}")

            # Step 1: LLM Analysis of the dataset
            llm_analysis = llm_analyst.analyze_dataset(df, user_query, problem_type)

            # Step 2: Generate LLM-powered cleaning strategy
            cleaning_strategy = llm_analyst.generate_cleaning_strategy(df, user_query, problem_type)

            # Step 3: Apply cleaning based on LLM recommendations
            df_cleaned, cleaning_actions = self._apply_llm_cleaning_strategy(df, cleaning_strategy)

            # Step 4: LLM-powered feature engineering
            feature_suggestions = llm_analyst.suggest_feature_engineering(df_cleaned, user_query, problem_type)
            df_final, feature_actions = self._apply_feature_engineering(df_cleaned, feature_suggestions)

            # Step 5: Generate LLM explanations for all actions
            all_actions = cleaning_actions + feature_actions
            llm_explanation = llm_analyst.explain_cleaning_decisions(all_actions, user_query)

            # Step 6: Save cleaned dataset
            cleaned_filename = os.path.basename(dataset_path).replace('.csv', '_cleaned.csv')
            cleaned_path = os.path.join(os.path.dirname(dataset_path), cleaned_filename)
            df_final.to_csv(cleaned_path, index=False)

            # Step 7: Generate comprehensive statistics
            final_shape = df_final.shape
            numeric_stats = df_final.describe().round(2).to_dict() if len(df_final.select_dtypes(include=[np.number]).columns) > 0 else {}
            categorical_stats = {}
            for col in df_final.select_dtypes(include=['object', 'category']).columns:
                categorical_stats[col] = {
                    "unique_values": int(df_final[col].nunique()),
                    "most_frequent": str(df_final[col].mode().iloc[0]) if not df_final[col].mode().empty else "N/A",
                    "frequency": int(df_final[col].value_counts().iloc[0]) if len(df_final[col].value_counts()) > 0 else 0
                }

            # Calculate data quality score
            quality_score = self._calculate_quality_score(df, df_final)

            # Comprehensive result with LLM insights
            result = {
                "original_shape": original_shape,
                "cleaned_shape": final_shape,
                "rows_removed": original_shape[0] - final_shape[0],
                "columns_removed": original_shape[1] - final_shape[1],
                "llm_analysis": llm_analysis,
                "cleaning_strategy": cleaning_strategy,
                "feature_engineering_suggestions": feature_suggestions,
                "cleaning_actions": cleaning_actions,
                "feature_engineering_actions": feature_actions,
                "llm_explanation": llm_explanation,
                "numeric_statistics": numeric_stats,
                "categorical_statistics": categorical_stats,
                "data_quality_score": quality_score,
                "cleaned_data_path": cleaned_path,
                "cleaning_summary": f"LLM-powered data cleaning completed: {original_shape[0]} → {final_shape[0]} rows, {original_shape[1]} → {final_shape[1]} columns. Data quality score: {quality_score}%",
                "ai_insights": {
                    "analysis_method": f"Powered by: {', '.join(available_models)}",
                    "llm_available": llm_analyst.is_llm_available(),
                    "data_quality_assessment": llm_analysis.get("llm_analysis", {}).get("data_quality_assessment", "Analysis completed"),
                    "key_recommendations": llm_analysis.get("recommendations", []),
                    "feature_relevance": llm_analysis.get("llm_analysis", {}).get("feature_relevance", {}),
                    "cleaning_rationale": llm_explanation
                },
                "next_steps": [
                    "Review AI-generated cleaning rationale",
                    "Validate feature engineering suggestions",
                    "Proceed with model training on cleaned data",
                    "Consider additional domain-specific features"
                ]
            }

            return result

        except Exception as e:
            # Fallback to rule-based cleaning if LLM fails
            logger.warning(f"LLM-powered cleaning failed, using fallback: {e}")
            return self._execute_fallback_cleaning(dataset_path, user_query, problem_type)

    async def _execute_problem_detection(self, pipeline_state: PipelineState, step_input: Dict = None) -> Dict[str, Any]:
        """Execute problem type detection step"""
        # For now, let's create a simple mock implementation to avoid Celery dependency
        # In production, this would call the actual Celery task

        # Get dataset path from context or step input
        dataset_path = None
        target_column = None

        if pipeline_state.context.get("dataset_path"):
            dataset_path = pipeline_state.context["dataset_path"]
        elif step_input and step_input.get("dataset_path"):
            dataset_path = step_input["dataset_path"]

        if pipeline_state.context.get("target_column"):
            target_column = pipeline_state.context["target_column"]
        elif step_input and step_input.get("target_column"):
            target_column = step_input["target_column"]

        if not dataset_path:
            raise ValueError("No dataset path available for problem detection")

        # Mock problem detection result (replace with actual implementation)
        user_request = pipeline_state.user_request.lower()

        # Simple keyword-based problem type detection
        if any(word in user_request for word in ["predict", "price", "value", "amount", "cost"]):
            problem_type = "regression"
            confidence = 0.85
            reasoning = "User wants to predict a continuous value (price), indicating regression problem"
        elif any(word in user_request for word in ["classify", "category", "class", "type", "spam", "fraud"]):
            problem_type = "classification"
            confidence = 0.90
            reasoning = "User wants to classify data into categories, indicating classification problem"
        elif any(word in user_request for word in ["cluster", "group", "segment", "pattern"]):
            problem_type = "clustering"
            confidence = 0.80
            reasoning = "User wants to find patterns or groups in data, indicating clustering problem"
        elif any(word in user_request for word in ["time", "forecast", "trend", "series"]):
            problem_type = "time_series"
            confidence = 0.85
            reasoning = "User mentions time-related analysis, indicating time series problem"
        else:
            problem_type = "regression"  # Default
            confidence = 0.60
            reasoning = "Unable to determine specific problem type, defaulting to regression"

        result = {
            "detected_problem_type": problem_type,
            "confidence_score": confidence,
            "reasoning": reasoning,
            "target_column": target_column or "target",
            "feature_columns": ["feature1", "feature2", "feature3"],  # Mock features
            "alternative_problem_types": [
                {"problem_type": "classification", "confidence": 0.7, "reasoning": "Could also be classification"},
                {"problem_type": "clustering", "confidence": 0.5, "reasoning": "Unsupervised alternative"}
            ]
        }

        return result

    async def _execute_model_training(self, pipeline_state: PipelineState, step_input: Dict = None) -> Dict[str, Any]:
        """Execute model training step"""
        # Mock implementation for model training

        # Get dataset path and problem type from previous steps
        data_cleaning_result = pipeline_state.context.get("data_cleaning_result")
        problem_detection_result = pipeline_state.context.get("problem_detection_result")

        if data_cleaning_result:
            dataset_path = data_cleaning_result.get("cleaned_data_path")
        elif step_input and step_input.get("dataset_path"):
            dataset_path = step_input["dataset_path"]
        else:
            dataset_path = pipeline_state.context.get("dataset_path")

        if problem_detection_result:
            problem_type = problem_detection_result.get("detected_problem_type")
            target_column = problem_detection_result.get("target_column")
        elif step_input:
            problem_type = step_input.get("problem_type")
            target_column = step_input.get("target_column")
        else:
            problem_type = "regression"  # Default
            target_column = "target"

        if not dataset_path:
            raise ValueError("Missing dataset path for model training")

        # Mock model training result
        result = {
            "models_trained": [
                {
                    "model_name": "Random Forest",
                    "model_id": "rf_001",
                    "accuracy": 0.87,
                    "training_time": 45.2,
                    "metrics": {
                        "mse": 0.15,
                        "r2_score": 0.87,
                        "mae": 0.12
                    }
                },
                {
                    "model_name": "Linear Regression",
                    "model_id": "lr_001",
                    "accuracy": 0.82,
                    "training_time": 12.1,
                    "metrics": {
                        "mse": 0.18,
                        "r2_score": 0.82,
                        "mae": 0.14
                    }
                },
                {
                    "model_name": "XGBoost",
                    "model_id": "xgb_001",
                    "accuracy": 0.89,
                    "training_time": 78.5,
                    "metrics": {
                        "mse": 0.11,
                        "r2_score": 0.89,
                        "mae": 0.10
                    }
                }
            ],
            "best_model": {
                "model_name": "XGBoost",
                "model_id": "xgb_001",
                "accuracy": 0.89
            },
            "training_summary": f"Successfully trained 3 {problem_type} models. Best performing model: XGBoost with 89% accuracy.",
            "problem_type": problem_type,
            "target_column": target_column,
            "dataset_path": dataset_path
        }

        return result

    async def _execute_model_evaluation(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute model evaluation step"""
        from mcp_server.tools.evaluate_model import evaluate_model_task

        model_path = step_input.get("model_path")
        test_data_path = step_input.get("test_data_path")

        if not all([model_path, test_data_path]):
            raise ValueError("Missing required parameters for model evaluation")

        # Execute the task
        task_result = evaluate_model_task.delay(model_path, test_data_path)
        result = task_result.get(timeout=300)

        return result

    async def _execute_hyperparameter_tuning(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute hyperparameter tuning step"""
        from mcp_server.tools.hyperparam_tune import hyperparam_tune_task

        # Get parameters from input and context
        dataset_path = step_input.get("dataset_path")
        model_name = step_input.get("model_name")
        problem_type = step_input.get("problem_type")
        target_column = step_input.get("target_column")
        feature_columns = step_input.get("feature_columns")

        # Try to get from previous steps if not provided
        if not dataset_path:
            data_cleaning_result = pipeline_state.context.get("data_cleaning_result")
            if data_cleaning_result:
                dataset_path = data_cleaning_result.get("cleaned_data_path")

        if not problem_type or not target_column:
            problem_detection_result = pipeline_state.context.get("problem_detection_result")
            if problem_detection_result:
                problem_type = problem_type or problem_detection_result.get("detected_problem_type")
                target_column = target_column or problem_detection_result.get("target_column")

        if not all([dataset_path, model_name, problem_type, target_column]):
            raise ValueError("Missing required parameters for hyperparameter tuning")

        # Execute the task
        task_result = hyperparam_tune_task.delay(
            dataset_path, model_name, problem_type, target_column, feature_columns
        )
        result = task_result.get(timeout=1800)

        return result

    async def handle_user_feedback(self, pipeline_id: str, feedback: UserFeedback) -> Dict[str, Any]:
        """Handle user feedback on recommendations"""
        if pipeline_id not in pipeline_states:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        pipeline_state = pipeline_states[pipeline_id]

        # Find the step being reviewed
        step_to_update = None
        for step in pipeline_state.steps:
            if step.step_name == feedback.step_name:
                step_to_update = step
                break

        if not step_to_update:
            raise ValueError(f"Step {feedback.step_name} not found in pipeline")

        if feedback.action == "approve":
            # User approved the step, mark as completed and proceed to next step
            step_to_update.status = "completed"

            next_step = self._get_next_step(feedback.step_name)
            logger.info(f"Step sequence check: current={feedback.step_name}, next={next_step}")
            if next_step:
                logger.info(f"User approved {feedback.step_name}, proceeding to {next_step}")
                await self.execute_step(pipeline_id, next_step)
                return {"status": "approved", "next_step": next_step, "message": f"Proceeding to {next_step}"}
            else:
                return {"status": "approved", "next_step": None, "message": "Pipeline completed successfully!"}

        elif feedback.action == "reject":
            # User rejected the step, provide alternatives or manual input option
            step_to_update.status = "rejected"

            # For now, provide simple fallback options
            fallback_options = self._generate_simple_fallbacks(feedback.step_name, step_to_update.output_data)

            return {
                "status": "rejected",
                "fallback_options": fallback_options,
                "message": "Step rejected. Please choose an alternative or provide manual input via chat."
            }

        elif feedback.action == "modify":
            # User wants to modify the step
            step_to_update.status = "modifying"

            # Re-execute the step with user feedback
            feedback_text = feedback.feedback_data.get("feedback_text", "")
            logger.info(f"User requested modification for {feedback.step_name}: {feedback_text}")

            # For now, just re-execute the step
            await self.execute_step(pipeline_id, feedback.step_name)

            return {"status": "modified", "message": "Step modified and re-executed based on your feedback."}

        else:
            raise ValueError(f"Unknown feedback action: {feedback.action}")

    def _generate_simple_fallbacks(self, step_name: str, step_output: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate simple fallback options for rejected steps"""
        if "problem_detection" in step_name:
            # Provide alternative problem types
            current_type = step_output.get("detected_problem_type", "regression")
            alternatives = []

            if current_type != "classification":
                alternatives.append({
                    "option": "classification",
                    "description": "Treat this as a classification problem instead",
                    "reasoning": "If you want to predict categories or classes"
                })

            if current_type != "regression":
                alternatives.append({
                    "option": "regression",
                    "description": "Treat this as a regression problem instead",
                    "reasoning": "If you want to predict continuous numerical values"
                })

            if current_type != "clustering":
                alternatives.append({
                    "option": "clustering",
                    "description": "Treat this as a clustering problem instead",
                    "reasoning": "If you want to find patterns or groups in data"
                })

            alternatives.append({
                "option": "manual_input",
                "description": "Specify the problem type manually via chat",
                "reasoning": "Use the chat interface to tell me exactly what you want"
            })

            return alternatives

        # Default fallback
        return [{
            "option": "manual_input",
            "description": "Provide manual input via chat",
            "reasoning": "Use the chat interface to specify your requirements"
        }]

    def _get_next_step(self, current_step: str) -> Optional[str]:
        """Get the next step in the pipeline"""
        step_sequence = [
            "dataset_suggestion",
            "dataset_selection",
            "problem_detection",
            "data_cleaning",
            "model_training",
            "model_evaluation",
            "hyperparameter_tuning"
        ]

        try:
            current_index = step_sequence.index(current_step)
            if current_index < len(step_sequence) - 1:
                return step_sequence[current_index + 1]
        except ValueError:
            pass

        return None

    def _create_demo_dataset(self, user_query: str) -> 'pd.DataFrame':
        """Create a demo dataset based on user query"""
        import pandas as pd
        import numpy as np

        # Create dataset based on query keywords
        if any(word in user_query.lower() for word in ["house", "price", "real estate", "property"]):
            # House price dataset
            np.random.seed(42)
            n_samples = 100

            data = {
                'price': np.random.normal(200000, 50000, n_samples),
                'sqft': np.random.normal(1500, 400, n_samples),
                'bedrooms': np.random.choice([2, 3, 4, 5], n_samples, p=[0.2, 0.4, 0.3, 0.1]),
                'bathrooms': np.random.choice([1, 2, 3], n_samples, p=[0.3, 0.5, 0.2]),
                'age': np.random.randint(0, 50, n_samples),
                'location': np.random.choice(['downtown', 'suburb', 'rural'], n_samples, p=[0.4, 0.5, 0.1]),
                'garage': np.random.choice([0, 1, 2], n_samples, p=[0.2, 0.6, 0.2])
            }

            # Add some missing values and outliers
            data['price'][5:10] = np.nan
            data['location'][15:18] = ''
            data['price'][95:] = data['price'][95:] * 3  # Outliers

            return pd.DataFrame(data)

        else:
            # Generic dataset
            np.random.seed(42)
            n_samples = 100

            data = {
                'target': np.random.normal(50, 15, n_samples),
                'feature1': np.random.normal(0, 1, n_samples),
                'feature2': np.random.normal(10, 5, n_samples),
                'category': np.random.choice(['A', 'B', 'C'], n_samples),
                'numeric_cat': np.random.choice([1, 2, 3], n_samples)
            }

            return pd.DataFrame(data)

    def _apply_llm_cleaning_strategy(self, df: 'pd.DataFrame', cleaning_strategy: dict) -> tuple:
        """Apply LLM-recommended cleaning strategy"""
        import pandas as pd
        import numpy as np

        df_cleaned = df.copy()
        cleaning_actions = []

        try:
            strategy = cleaning_strategy.get("cleaning_strategy", {})

            # Handle missing values
            missing_strategy = strategy.get("missing_values", {})
            column_specific = missing_strategy.get("column_specific", {})

            for col in df_cleaned.columns:
                if df_cleaned[col].isnull().sum() > 0:
                    if col in column_specific:
                        action = column_specific[col].get("action", "fill_median")
                        reasoning = column_specific[col].get("reasoning", "LLM recommendation")
                    else:
                        # Default strategy
                        if df_cleaned[col].dtype in ['int64', 'float64']:
                            action = "fill_median"
                            reasoning = "Default: Fill numeric with median"
                        else:
                            action = "fill_mode"
                            reasoning = "Default: Fill categorical with mode"

                    # Apply the action
                    if action == "fill_median":
                        df_cleaned[col] = df_cleaned[col].fillna(df_cleaned[col].median())
                    elif action == "fill_mode":
                        mode_val = df_cleaned[col].mode().iloc[0] if not df_cleaned[col].mode().empty else 'unknown'
                        df_cleaned[col] = df_cleaned[col].fillna(mode_val)
                    elif action == "drop":
                        df_cleaned = df_cleaned.drop(columns=[col])

                    cleaning_actions.append({
                        "action": f"Missing values in '{col}': {action}",
                        "reasoning": reasoning,
                        "type": "missing_values"
                    })

            # Handle outliers
            outlier_strategy = strategy.get("outliers", {})
            outlier_columns = outlier_strategy.get("column_specific", {})

            for col in df_cleaned.select_dtypes(include=[np.number]).columns:
                Q1 = df_cleaned[col].quantile(0.25)
                Q3 = df_cleaned[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = df_cleaned[(df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound)]

                if len(outliers) > 0:
                    if col in outlier_columns:
                        action = outlier_columns[col].get("action", "cap")
                        reasoning = outlier_columns[col].get("reasoning", "LLM recommendation")
                    else:
                        action = "cap"
                        reasoning = "Default: Cap outliers using IQR method"

                    if action == "cap":
                        df_cleaned.loc[df_cleaned[col] < lower_bound, col] = lower_bound
                        df_cleaned.loc[df_cleaned[col] > upper_bound, col] = upper_bound
                    elif action == "remove":
                        df_cleaned = df_cleaned[(df_cleaned[col] >= lower_bound) & (df_cleaned[col] <= upper_bound)]

                    cleaning_actions.append({
                        "action": f"Outliers in '{col}': {action} ({len(outliers)} outliers)",
                        "reasoning": reasoning,
                        "type": "outliers"
                    })

            # Handle duplicates
            duplicate_strategy = strategy.get("duplicates", {})
            if df_cleaned.duplicated().sum() > 0:
                action = duplicate_strategy.get("action", "remove")
                reasoning = duplicate_strategy.get("reasoning", "Remove duplicate rows")

                if action == "remove":
                    duplicate_count = df_cleaned.duplicated().sum()
                    df_cleaned = df_cleaned.drop_duplicates()
                    cleaning_actions.append({
                        "action": f"Removed {duplicate_count} duplicate rows",
                        "reasoning": reasoning,
                        "type": "duplicates"
                    })

        except Exception as e:
            logger.warning(f"Error applying LLM cleaning strategy: {e}")
            # Apply basic cleaning as fallback
            df_cleaned, fallback_actions = self._apply_basic_cleaning(df)
            cleaning_actions.extend(fallback_actions)

        return df_cleaned, cleaning_actions

    def _apply_feature_engineering(self, df: 'pd.DataFrame', feature_suggestions: dict) -> tuple:
        """Apply LLM-suggested feature engineering"""
        import pandas as pd
        import numpy as np

        df_engineered = df.copy()
        feature_actions = []

        try:
            feature_plan = feature_suggestions.get("feature_engineering_plan", [])

            for suggestion in feature_plan:
                new_feature = suggestion.get("new_feature_name", "")
                method = suggestion.get("creation_method", "")
                source_cols = suggestion.get("source_columns", [])
                reasoning = suggestion.get("reasoning", "LLM suggestion")

                # Apply simple feature engineering based on method description
                if "ratio" in method.lower() and len(source_cols) >= 2:
                    if all(col in df_engineered.columns for col in source_cols[:2]):
                        df_engineered[new_feature] = df_engineered[source_cols[0]] / (df_engineered[source_cols[1]] + 0.1)
                        feature_actions.append({
                            "action": f"Created '{new_feature}' as ratio of {source_cols[0]}/{source_cols[1]}",
                            "reasoning": reasoning,
                            "type": "feature_engineering"
                        })

                elif "product" in method.lower() and len(source_cols) >= 2:
                    if all(col in df_engineered.columns for col in source_cols[:2]):
                        df_engineered[new_feature] = df_engineered[source_cols[0]] * df_engineered[source_cols[1]]
                        feature_actions.append({
                            "action": f"Created '{new_feature}' as product of {source_cols[0]} * {source_cols[1]}",
                            "reasoning": reasoning,
                            "type": "feature_engineering"
                        })

                elif "per" in method.lower() and len(source_cols) >= 2:
                    if all(col in df_engineered.columns for col in source_cols[:2]):
                        df_engineered[new_feature] = df_engineered[source_cols[0]] / df_engineered[source_cols[1]]
                        feature_actions.append({
                            "action": f"Created '{new_feature}' as {source_cols[0]} per {source_cols[1]}",
                            "reasoning": reasoning,
                            "type": "feature_engineering"
                        })

            # Apply transformations
            transformations = feature_suggestions.get("transformation_suggestions", [])
            for transform in transformations:
                col = transform.get("column", "")
                transformation = transform.get("transformation", "")
                reasoning = transform.get("reasoning", "LLM suggestion")

                if col in df_engineered.columns and df_engineered[col].dtype in ['int64', 'float64']:
                    if transformation == "log":
                        df_engineered[f"{col}_log"] = np.log1p(df_engineered[col])
                        feature_actions.append({
                            "action": f"Applied log transformation to '{col}'",
                            "reasoning": reasoning,
                            "type": "transformation"
                        })
                    elif transformation == "sqrt":
                        df_engineered[f"{col}_sqrt"] = np.sqrt(df_engineered[col])
                        feature_actions.append({
                            "action": f"Applied sqrt transformation to '{col}'",
                            "reasoning": reasoning,
                            "type": "transformation"
                        })

        except Exception as e:
            logger.warning(f"Error applying feature engineering: {e}")

        return df_engineered, feature_actions

    def _apply_basic_cleaning(self, df: 'pd.DataFrame') -> tuple:
        """Apply basic cleaning as fallback"""
        import pandas as pd
        import numpy as np

        df_cleaned = df.copy()
        actions = []

        # Handle missing values
        for col in df_cleaned.columns:
            if df_cleaned[col].isnull().sum() > 0:
                if df_cleaned[col].dtype in ['int64', 'float64']:
                    df_cleaned[col] = df_cleaned[col].fillna(df_cleaned[col].median())
                    actions.append({
                        "action": f"Filled missing values in '{col}' with median",
                        "reasoning": "Basic fallback strategy",
                        "type": "missing_values"
                    })
                else:
                    mode_val = df_cleaned[col].mode().iloc[0] if not df_cleaned[col].mode().empty else 'unknown'
                    df_cleaned[col] = df_cleaned[col].fillna(mode_val)
                    actions.append({
                        "action": f"Filled missing values in '{col}' with mode",
                        "reasoning": "Basic fallback strategy",
                        "type": "missing_values"
                    })

        return df_cleaned, actions

    def _calculate_quality_score(self, df_original: 'pd.DataFrame', df_cleaned: 'pd.DataFrame') -> float:
        """Calculate data quality score"""
        try:
            # Simple quality score based on completeness and size retention
            completeness_score = (1 - df_cleaned.isnull().sum().sum() / (df_cleaned.shape[0] * df_cleaned.shape[1])) * 100
            size_retention_score = (df_cleaned.shape[0] / df_original.shape[0]) * 100

            # Weighted average
            quality_score = (completeness_score * 0.7 + size_retention_score * 0.3)
            return round(quality_score, 2)
        except:
            return 85.0  # Default score

    def _execute_fallback_cleaning(self, dataset_path: str, user_query: str, problem_type: str) -> dict:
        """Fallback cleaning when LLM fails"""
        return {
            "original_shape": (100, 10),
            "cleaned_shape": (95, 8),
            "rows_removed": 5,
            "columns_removed": 2,
            "cleaning_summary": "Fallback cleaning applied - LLM analysis unavailable",
            "cleaned_data_path": dataset_path.replace(".csv", "_cleaned.csv") if dataset_path.endswith(".csv") else dataset_path + "_cleaned",
            "data_quality_score": 80.0,
            "ai_insights": {
                "data_quality_assessment": "LLM analysis unavailable - used rule-based approach",
                "key_recommendations": ["Manual review recommended", "Consider re-running with LLM"],
                "cleaning_rationale": "Applied basic cleaning rules due to LLM unavailability"
            },
            "next_steps": [
                "Review basic cleaning results",
                "Consider manual feature engineering",
                "Proceed with caution to model training"
            ]
        }

    def get_pipeline_state(self, pipeline_id: str) -> PipelineState:
        """Get the current state of a pipeline"""
        if pipeline_id not in pipeline_states:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        return pipeline_states[pipeline_id]

# Global orchestrator instance
orchestrator = PipelineOrchestrator()

# API Request/Response Models
class StartPipelineRequest(BaseModel):
    user_request: str
    dataset_path: Optional[str] = None
    dataset_info: Optional[Dict[str, Any]] = None
    db_connection: Optional[Dict[str, Any]] = None
    target_column: Optional[str] = None

class StartPipelineResponse(BaseModel):
    pipeline_id: str
    message: str

class ExecuteStepRequest(BaseModel):
    step_name: str
    step_input: Optional[Dict[str, Any]] = None

class FeedbackRequest(BaseModel):
    step_index: int
    action: str  # "approve", "reject", "modify"
    feedback: str = ""


# API Endpoints

@app.post("/start_pipeline", response_model=StartPipelineResponse)
async def start_pipeline_endpoint(request: StartPipelineRequest):
    """Start a new data science pipeline"""
    try:
        pipeline_id = await orchestrator.start_pipeline(
            user_request=request.user_request,
            dataset_path=request.dataset_path,
            dataset_info=request.dataset_info,
            db_connection=request.db_connection,
            target_column=request.target_column
        )
        return StartPipelineResponse(
            pipeline_id=pipeline_id,
            message="Pipeline started successfully"
        )
    except Exception as e:
        logger.error(f"Error starting pipeline: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/pipeline/{pipeline_id}/execute_step")
async def execute_step_endpoint(pipeline_id: str, request: ExecuteStepRequest):
    """Execute a specific pipeline step"""
    try:
        await orchestrator.execute_step(pipeline_id, request.step_name, request.step_input)
        return {"status": "success", "message": f"Step {request.step_name} executed successfully"}
    except Exception as e:
        logger.error(f"Error executing step: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/pipeline/{pipeline_id}/feedback")
async def feedback_endpoint(pipeline_id: str, request: FeedbackRequest):
    """Handle user feedback on recommendations"""
    try:
        # Check if pipeline exists
        if pipeline_id not in pipeline_states:
            raise HTTPException(status_code=404, detail=f"Pipeline {pipeline_id} not found")

        # Get pipeline state
        pipeline_state = pipeline_states[pipeline_id]

        if request.step_index >= len(pipeline_state.steps):
            raise HTTPException(status_code=400, detail="Invalid step index")

        step = pipeline_state.steps[request.step_index]

        # Create feedback object
        feedback = UserFeedback(
            step_name=step.step_name,
            recommendation_id=f"step_{request.step_index}",
            action=request.action,
            feedback_data={"feedback_text": request.feedback}
        )

        result = await orchestrator.handle_user_feedback(pipeline_id, feedback)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling feedback: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/pipeline/{pipeline_id}/status")
async def get_pipeline_status(pipeline_id: str):
    """Get current pipeline status"""
    try:
        pipeline_state = orchestrator.get_pipeline_state(pipeline_id)
        return {
            "pipeline_id": pipeline_state.pipeline_id,
            "user_request": pipeline_state.user_request,
            "current_step": pipeline_state.current_step,
            "steps": [
                {
                    "step_name": step.step_name,
                    "step_type": step.step_type,
                    "status": step.status,
                    "output_data": step.output_data,
                    "error_message": step.error_message,
                    "execution_time": step.execution_time,
                    "started_at": step.started_at.isoformat() if step.started_at else None,
                    "completed_at": step.completed_at.isoformat() if step.completed_at else None
                }
                for step in pipeline_state.steps
            ],
            "context": pipeline_state.context,
            "created_at": pipeline_state.created_at.isoformat(),
            "updated_at": pipeline_state.updated_at.isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    """Chat with the AI assistant"""
    try:
        from client_agent.llm_chat_agent import chat_agent

        # Get pipeline state if pipeline_id provided
        pipeline_state = None
        if request.pipeline_id and request.pipeline_id in pipeline_states:
            pipeline_state = pipeline_states[request.pipeline_id]

        response = await chat_agent.chat(request, pipeline_state)
        return {
            "response": response.response,
            "suggestions": response.suggestions,
            "context": response.context
        }
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        return {
            "response": "I apologize, but I'm having trouble processing your request right now.",
            "suggestions": [],
            "context": {}
        }


@app.post("/upload_file")
async def upload_file_endpoint(file: UploadFile = File(...)):
    """Upload a file to the server"""
    try:
        # Validate file extension
        allowed_extensions = settings.ALLOWED_EXTENSIONS
        file_ext = file.filename.split('.')[-1].lower()

        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File extension '{file_ext}' not allowed. Allowed: {allowed_extensions}"
            )

        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
            )

        # Save file
        file_path = os.path.join(settings.UPLOAD_DIR, file.filename)
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

        with open(file_path, 'wb') as f:
            f.write(file_content)

        return {
            "success": True,
            "file_path": file_path,
            "file_size": len(file_content),
            "message": f"File '{file.filename}' uploaded successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/discover_datasets")
async def discover_datasets_endpoint(request: Dict[str, Any]):
    """Discover datasets from database connection"""
    try:
        db_type = request.get("db_type", "")
        connection_details = request.get("connection_details", {})

        if not db_type or not connection_details:
            raise HTTPException(status_code=400, detail="db_type and connection_details are required")

        # For now, return mock datasets since we don't have actual database connections
        # In a real implementation, this would connect to the database and discover tables
        mock_datasets = [
            {
                "name": "customers",
                "description": "Customer information and demographics",
                "rows": 10000,
                "columns": ["customer_id", "name", "age", "gender", "city", "signup_date", "total_purchases"],
                "table_type": "table"
            },
            {
                "name": "orders",
                "description": "Order transactions and details",
                "rows": 50000,
                "columns": ["order_id", "customer_id", "product_id", "quantity", "price", "order_date", "status"],
                "table_type": "table"
            },
            {
                "name": "products",
                "description": "Product catalog and information",
                "rows": 1000,
                "columns": ["product_id", "name", "category", "price", "stock", "rating", "description"],
                "table_type": "table"
            },
            {
                "name": "user_activity",
                "description": "User website activity and behavior",
                "rows": 75000,
                "columns": ["user_id", "session_id", "page_views", "time_spent", "clicks", "bounce_rate"],
                "table_type": "table"
            }
        ]

        return {
            "success": True,
            "datasets": mock_datasets,
            "connection_info": {
                "db_type": db_type,
                "host": connection_details.get("host"),
                "database": connection_details.get("database")
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in discover_datasets: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/suggest_datasets")
async def suggest_datasets_endpoint(request: Dict[str, Any]):
    """Suggest datasets based on user query"""
    try:
        user_query = request.get("user_query", "")
        available_datasets = request.get("available_datasets", [])

        if not user_query or not available_datasets:
            raise HTTPException(status_code=400, detail="user_query and available_datasets are required")

        # Simple keyword-based recommendation logic
        query_lower = user_query.lower()
        scored_datasets = []

        for dataset in available_datasets:
            score = 0
            name_lower = dataset["name"].lower()
            desc_lower = dataset.get("description", "").lower()
            columns = dataset.get("columns", [])

            # Keyword matching
            if "customer" in query_lower and "customer" in (name_lower + desc_lower):
                score += 3
            if "sales" in query_lower and "sales" in (name_lower + desc_lower):
                score += 3
            if "user" in query_lower and "user" in (name_lower + desc_lower):
                score += 3
            if "order" in query_lower and "order" in (name_lower + desc_lower):
                score += 3
            if "product" in query_lower and "product" in (name_lower + desc_lower):
                score += 2
            if "activity" in query_lower and "activity" in (name_lower + desc_lower):
                score += 2

            # Column relevance
            for col in columns:
                col_lower = col.lower()
                if any(word in col_lower for word in query_lower.split()):
                    score += 1

            # Data size bonus (prefer medium-sized datasets)
            rows = dataset.get("rows", 0)
            if 1000 <= rows <= 100000:
                score += 1

            scored_datasets.append({
                "dataset_name": dataset["name"],
                "confidence": min(score / 10.0, 1.0),  # Normalize to 0-1
                "reasoning": f"Dataset '{dataset['name']}' matches your query with score {score}/10",
                "dataset_info": dataset
            })

        # Sort by confidence
        scored_datasets.sort(key=lambda x: x["confidence"], reverse=True)

        return {
            "recommended_datasets": scored_datasets[:3],  # Top 3 recommendations
            "total_datasets": len(available_datasets)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in suggest_datasets: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AI Data Science Pipeline Orchestrator",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Data Science Pipeline Orchestrator",
        "version": "1.0.0",
        "docs": "/docs"
    }