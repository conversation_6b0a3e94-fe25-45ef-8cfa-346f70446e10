"""
Orchestrator for AI Data Science Pipeline
Manages the workflow and coordinates between different tools
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import sys
import os
from loguru import logger

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings, PROBLEM_TYPES
from mcp_server.models import (
    PipelineState, PipelineStep, TaskStatus, ProblemType,
    UserFeedback, ChatRequest, ChatResponse
)

# Initialize FastAPI app
app = FastAPI(
    title="AI Data Science Pipeline Orchestrator",
    description="Orchestrates the AI-powered data science workflow",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for pipeline states (in production, use Redis or database)
pipeline_states: Dict[str, PipelineState] = {}

class PipelineOrchestrator:
    """Main orchestrator class for managing the data science pipeline"""

    def __init__(self):
        self.mcp_client = None  # Will be initialized when needed

    async def start_pipeline(self, user_request: str, dataset_path: Optional[str] = None,
                           dataset_info: Optional[Dict] = None, db_connection: Optional[Dict] = None,
                           target_column: Optional[str] = None) -> str:
        """Start a new data science pipeline"""
        pipeline_id = str(uuid.uuid4())

        # Determine starting step based on data source
        if dataset_path:
            # File uploaded - skip dataset suggestion and go to problem detection
            starting_step = "problem_detection"
            context = {
                "user_request": user_request,
                "dataset_path": dataset_path,
                "target_column": target_column,
                "data_source": "file"
            }
        elif dataset_info:
            # Dataset selected from database - skip to problem detection
            starting_step = "problem_detection"
            context = {
                "user_request": user_request,
                "dataset_info": dataset_info,
                "db_connection": db_connection,
                "target_column": target_column,
                "data_source": "database"
            }
        else:
            # No dataset provided - start with dataset suggestion
            starting_step = "dataset_suggestion"
            context = {
                "user_request": user_request,
                "data_source": "unknown"
            }

        # Create initial pipeline state
        pipeline_state = PipelineState(
            pipeline_id=pipeline_id,
            user_request=user_request,
            current_step=starting_step,
            steps=[],
            context=context
        )

        pipeline_states[pipeline_id] = pipeline_state

        logger.info(f"Started new pipeline: {pipeline_id} with starting step: {starting_step}")

        # Start with the appropriate first step
        await self.execute_step(pipeline_id, starting_step)

        return pipeline_id

    async def execute_step(self, pipeline_id: str, step_name: str, step_input: Optional[Dict] = None):
        """Execute a specific pipeline step"""
        if pipeline_id not in pipeline_states:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        pipeline_state = pipeline_states[pipeline_id]

        # Create step record
        step = PipelineStep(
            step_name=step_name,
            step_type=self._get_step_type(step_name),
            status=TaskStatus.RUNNING,
            input_data=step_input,
            started_at=datetime.now()
        )

        pipeline_state.steps.append(step)
        pipeline_state.current_step = step_name
        pipeline_state.updated_at = datetime.now()

        try:
            # Execute the step based on its name
            if step_name == "dataset_suggestion":
                result = await self._execute_dataset_suggestion(pipeline_state)
            elif step_name == "dataset_selection":
                result = await self._execute_dataset_selection(pipeline_state, step_input)
            elif step_name == "data_cleaning":
                result = await self._execute_data_cleaning(pipeline_state, step_input)
            elif step_name == "problem_detection":
                result = await self._execute_problem_detection(pipeline_state, step_input)
            elif step_name == "model_training":
                result = await self._execute_model_training(pipeline_state, step_input)
            elif step_name == "model_evaluation":
                result = await self._execute_model_evaluation(pipeline_state, step_input)
            elif step_name == "hyperparameter_tuning":
                result = await self._execute_hyperparameter_tuning(pipeline_state, step_input)
            else:
                raise ValueError(f"Unknown step: {step_name}")

            # Update step with success
            step.status = TaskStatus.SUCCESS
            step.output_data = result
            step.completed_at = datetime.now()
            step.execution_time = (step.completed_at - step.started_at).total_seconds()

            # Update pipeline context
            pipeline_state.context[f"{step_name}_result"] = result

            logger.info(f"Step {step_name} completed successfully for pipeline {pipeline_id}")

        except Exception as e:
            # Update step with failure
            step.status = TaskStatus.FAILED
            step.error_message = str(e)
            step.completed_at = datetime.now()
            step.execution_time = (step.completed_at - step.started_at).total_seconds()

            logger.error(f"Step {step_name} failed for pipeline {pipeline_id}: {e}")
            raise

    def _get_step_type(self, step_name: str) -> str:
        """Get the type of a pipeline step"""
        step_types = {
            "dataset_suggestion": "data_discovery",
            "dataset_selection": "data_discovery",
            "data_cleaning": "data_preprocessing",
            "problem_detection": "problem_analysis",
            "model_training": "model_development",
            "model_evaluation": "model_evaluation",
            "hyperparameter_tuning": "model_optimization"
        }
        return step_types.get(step_name, "unknown")

    async def _execute_dataset_suggestion(self, pipeline_state: PipelineState) -> Dict[str, Any]:
        """Execute dataset suggestion step"""
        from mcp_server.tools.suggest_datasets import suggest_datasets_task

        user_request = pipeline_state.user_request

        # Execute the task
        task_result = suggest_datasets_task.delay(user_request)
        result = task_result.get(timeout=60)

        return result

    async def _execute_dataset_selection(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute dataset selection step"""
        selected_dataset = step_input.get("selected_dataset")
        if not selected_dataset:
            raise ValueError("No dataset selected")

        # Store selected dataset in context
        return {
            "selected_dataset": selected_dataset,
            "dataset_path": selected_dataset.get("file_path") or selected_dataset.get("table_name")
        }

    async def _execute_data_cleaning(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute data cleaning step"""
        from mcp_server.tools.clean_data import clean_data_task

        dataset_path = step_input.get("dataset_path")
        cleaning_options = step_input.get("cleaning_options", {})

        if not dataset_path:
            # Try to get from previous step
            dataset_selection_result = pipeline_state.context.get("dataset_selection_result")
            if dataset_selection_result:
                dataset_path = dataset_selection_result.get("dataset_path")

        if not dataset_path:
            raise ValueError("No dataset path available for cleaning")

        # Execute the task
        task_result = clean_data_task.delay(dataset_path, cleaning_options)
        result = task_result.get(timeout=300)

        return result

    async def _execute_problem_detection(self, pipeline_state: PipelineState, step_input: Dict = None) -> Dict[str, Any]:
        """Execute problem type detection step"""
        # For now, let's create a simple mock implementation to avoid Celery dependency
        # In production, this would call the actual Celery task

        # Get dataset path from context or step input
        dataset_path = None
        target_column = None

        if pipeline_state.context.get("dataset_path"):
            dataset_path = pipeline_state.context["dataset_path"]
        elif step_input and step_input.get("dataset_path"):
            dataset_path = step_input["dataset_path"]

        if pipeline_state.context.get("target_column"):
            target_column = pipeline_state.context["target_column"]
        elif step_input and step_input.get("target_column"):
            target_column = step_input["target_column"]

        if not dataset_path:
            raise ValueError("No dataset path available for problem detection")

        # Mock problem detection result (replace with actual implementation)
        user_request = pipeline_state.user_request.lower()

        # Simple keyword-based problem type detection
        if any(word in user_request for word in ["predict", "price", "value", "amount", "cost"]):
            problem_type = "regression"
            confidence = 0.85
            reasoning = "User wants to predict a continuous value (price), indicating regression problem"
        elif any(word in user_request for word in ["classify", "category", "class", "type", "spam", "fraud"]):
            problem_type = "classification"
            confidence = 0.90
            reasoning = "User wants to classify data into categories, indicating classification problem"
        elif any(word in user_request for word in ["cluster", "group", "segment", "pattern"]):
            problem_type = "clustering"
            confidence = 0.80
            reasoning = "User wants to find patterns or groups in data, indicating clustering problem"
        elif any(word in user_request for word in ["time", "forecast", "trend", "series"]):
            problem_type = "time_series"
            confidence = 0.85
            reasoning = "User mentions time-related analysis, indicating time series problem"
        else:
            problem_type = "regression"  # Default
            confidence = 0.60
            reasoning = "Unable to determine specific problem type, defaulting to regression"

        result = {
            "detected_problem_type": problem_type,
            "confidence_score": confidence,
            "reasoning": reasoning,
            "target_column": target_column or "target",
            "feature_columns": ["feature1", "feature2", "feature3"],  # Mock features
            "alternative_problem_types": [
                {"problem_type": "classification", "confidence": 0.7, "reasoning": "Could also be classification"},
                {"problem_type": "clustering", "confidence": 0.5, "reasoning": "Unsupervised alternative"}
            ]
        }

        return result

    async def _execute_model_training(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute model training step"""
        from mcp_server.tools.train_multiple_models import train_multiple_models_task

        # Get dataset path and problem type from previous steps
        data_cleaning_result = pipeline_state.context.get("data_cleaning_result")
        problem_detection_result = pipeline_state.context.get("problem_detection_result")

        if data_cleaning_result:
            dataset_path = data_cleaning_result.get("cleaned_data_path")
        else:
            dataset_path = step_input.get("dataset_path")

        if problem_detection_result:
            problem_type = problem_detection_result.get("detected_problem_type")
            target_column = problem_detection_result.get("target_column")
        else:
            problem_type = step_input.get("problem_type")
            target_column = step_input.get("target_column")

        feature_columns = step_input.get("feature_columns")
        models_to_train = step_input.get("models_to_train")
        test_size = step_input.get("test_size", 0.2)

        if not all([dataset_path, problem_type, target_column]):
            raise ValueError("Missing required parameters for model training")

        # Execute the task
        task_result = train_multiple_models_task.delay(
            dataset_path, problem_type, target_column,
            feature_columns, models_to_train, test_size
        )
        result = task_result.get(timeout=1800)

        return result

    async def _execute_model_evaluation(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute model evaluation step"""
        from mcp_server.tools.evaluate_model import evaluate_model_task

        model_path = step_input.get("model_path")
        test_data_path = step_input.get("test_data_path")

        if not all([model_path, test_data_path]):
            raise ValueError("Missing required parameters for model evaluation")

        # Execute the task
        task_result = evaluate_model_task.delay(model_path, test_data_path)
        result = task_result.get(timeout=300)

        return result

    async def _execute_hyperparameter_tuning(self, pipeline_state: PipelineState, step_input: Dict) -> Dict[str, Any]:
        """Execute hyperparameter tuning step"""
        from mcp_server.tools.hyperparam_tune import hyperparam_tune_task

        # Get parameters from input and context
        dataset_path = step_input.get("dataset_path")
        model_name = step_input.get("model_name")
        problem_type = step_input.get("problem_type")
        target_column = step_input.get("target_column")
        feature_columns = step_input.get("feature_columns")

        # Try to get from previous steps if not provided
        if not dataset_path:
            data_cleaning_result = pipeline_state.context.get("data_cleaning_result")
            if data_cleaning_result:
                dataset_path = data_cleaning_result.get("cleaned_data_path")

        if not problem_type or not target_column:
            problem_detection_result = pipeline_state.context.get("problem_detection_result")
            if problem_detection_result:
                problem_type = problem_type or problem_detection_result.get("detected_problem_type")
                target_column = target_column or problem_detection_result.get("target_column")

        if not all([dataset_path, model_name, problem_type, target_column]):
            raise ValueError("Missing required parameters for hyperparameter tuning")

        # Execute the task
        task_result = hyperparam_tune_task.delay(
            dataset_path, model_name, problem_type, target_column, feature_columns
        )
        result = task_result.get(timeout=1800)

        return result

    async def handle_user_feedback(self, pipeline_id: str, feedback: UserFeedback) -> Dict[str, Any]:
        """Handle user feedback on recommendations"""
        if pipeline_id not in pipeline_states:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        pipeline_state = pipeline_states[pipeline_id]

        if feedback.action == "approve":
            # User approved the recommendation, proceed to next step
            next_step = self._get_next_step(feedback.step_name)
            if next_step:
                await self.execute_step(pipeline_id, next_step, feedback.feedback_data)

            return {"status": "approved", "next_step": next_step}

        elif feedback.action == "reject":
            # User rejected the recommendation, generate fallbacks
            from mcp_server.tools.fallback_recommender import fallback_recommender_task

            # Get the rejected recommendation from the last step
            last_step = pipeline_state.steps[-1] if pipeline_state.steps else None
            rejected_recommendation = last_step.output_data if last_step else {}

            # Generate fallback recommendations
            task_result = fallback_recommender_task.delay(
                feedback.step_name, rejected_recommendation, pipeline_state.context
            )
            fallback_result = task_result.get(timeout=60)

            return {"status": "rejected", "fallback_recommendations": fallback_result}

        elif feedback.action == "modify":
            # User wants to modify the recommendation
            # Re-execute the step with modified parameters
            await self.execute_step(pipeline_id, feedback.step_name, feedback.feedback_data)

            return {"status": "modified", "step_re_executed": True}

        else:
            raise ValueError(f"Unknown feedback action: {feedback.action}")

    def _get_next_step(self, current_step: str) -> Optional[str]:
        """Get the next step in the pipeline"""
        step_sequence = [
            "dataset_suggestion",
            "dataset_selection",
            "data_cleaning",
            "problem_detection",
            "model_training",
            "model_evaluation",
            "hyperparameter_tuning"
        ]

        try:
            current_index = step_sequence.index(current_step)
            if current_index < len(step_sequence) - 1:
                return step_sequence[current_index + 1]
        except ValueError:
            pass

        return None

    def get_pipeline_state(self, pipeline_id: str) -> PipelineState:
        """Get the current state of a pipeline"""
        if pipeline_id not in pipeline_states:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        return pipeline_states[pipeline_id]

# Global orchestrator instance
orchestrator = PipelineOrchestrator()

# API Request/Response Models
class StartPipelineRequest(BaseModel):
    user_request: str
    dataset_path: Optional[str] = None
    dataset_info: Optional[Dict[str, Any]] = None
    db_connection: Optional[Dict[str, Any]] = None
    target_column: Optional[str] = None

class StartPipelineResponse(BaseModel):
    pipeline_id: str
    message: str

class ExecuteStepRequest(BaseModel):
    step_name: str
    step_input: Optional[Dict[str, Any]] = None

class FeedbackRequest(BaseModel):
    step_name: str
    recommendation_id: str
    action: str  # "approve", "reject", "modify"
    feedback_data: Optional[Dict[str, Any]] = None


# API Endpoints

@app.post("/start_pipeline", response_model=StartPipelineResponse)
async def start_pipeline_endpoint(request: StartPipelineRequest):
    """Start a new data science pipeline"""
    try:
        pipeline_id = await orchestrator.start_pipeline(
            user_request=request.user_request,
            dataset_path=request.dataset_path,
            dataset_info=request.dataset_info,
            db_connection=request.db_connection,
            target_column=request.target_column
        )
        return StartPipelineResponse(
            pipeline_id=pipeline_id,
            message="Pipeline started successfully"
        )
    except Exception as e:
        logger.error(f"Error starting pipeline: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/pipeline/{pipeline_id}/execute_step")
async def execute_step_endpoint(pipeline_id: str, request: ExecuteStepRequest):
    """Execute a specific pipeline step"""
    try:
        await orchestrator.execute_step(pipeline_id, request.step_name, request.step_input)
        return {"status": "success", "message": f"Step {request.step_name} executed successfully"}
    except Exception as e:
        logger.error(f"Error executing step: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/pipeline/{pipeline_id}/feedback")
async def feedback_endpoint(pipeline_id: str, request: FeedbackRequest):
    """Handle user feedback on recommendations"""
    try:
        feedback = UserFeedback(
            step_name=request.step_name,
            recommendation_id=request.recommendation_id,
            action=request.action,
            feedback_data=request.feedback_data
        )
        result = await orchestrator.handle_user_feedback(pipeline_id, feedback)
        return result
    except Exception as e:
        logger.error(f"Error handling feedback: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/pipeline/{pipeline_id}/status")
async def get_pipeline_status(pipeline_id: str):
    """Get current pipeline status"""
    try:
        pipeline_state = orchestrator.get_pipeline_state(pipeline_id)
        return {
            "pipeline_id": pipeline_state.pipeline_id,
            "user_request": pipeline_state.user_request,
            "current_step": pipeline_state.current_step,
            "steps": [
                {
                    "step_name": step.step_name,
                    "step_type": step.step_type,
                    "status": step.status,
                    "output_data": step.output_data,
                    "error_message": step.error_message,
                    "execution_time": step.execution_time,
                    "started_at": step.started_at.isoformat() if step.started_at else None,
                    "completed_at": step.completed_at.isoformat() if step.completed_at else None
                }
                for step in pipeline_state.steps
            ],
            "context": pipeline_state.context,
            "created_at": pipeline_state.created_at.isoformat(),
            "updated_at": pipeline_state.updated_at.isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    """Chat with the AI assistant"""
    try:
        from client_agent.llm_chat_agent import chat_agent

        # Get pipeline state if pipeline_id provided
        pipeline_state = None
        if request.pipeline_id and request.pipeline_id in pipeline_states:
            pipeline_state = pipeline_states[request.pipeline_id]

        response = await chat_agent.chat(request, pipeline_state)
        return {
            "response": response.response,
            "suggestions": response.suggestions,
            "context": response.context
        }
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        return {
            "response": "I apologize, but I'm having trouble processing your request right now.",
            "suggestions": [],
            "context": {}
        }


@app.post("/upload_file")
async def upload_file_endpoint(file: UploadFile = File(...)):
    """Upload a file to the server"""
    try:
        # Validate file extension
        allowed_extensions = settings.ALLOWED_EXTENSIONS
        file_ext = file.filename.split('.')[-1].lower()

        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File extension '{file_ext}' not allowed. Allowed: {allowed_extensions}"
            )

        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
            )

        # Save file
        file_path = os.path.join(settings.UPLOAD_DIR, file.filename)
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

        with open(file_path, 'wb') as f:
            f.write(file_content)

        return {
            "success": True,
            "file_path": file_path,
            "file_size": len(file_content),
            "message": f"File '{file.filename}' uploaded successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/discover_datasets")
async def discover_datasets_endpoint(request: Dict[str, Any]):
    """Discover datasets from database connection"""
    try:
        db_type = request.get("db_type", "")
        connection_details = request.get("connection_details", {})

        if not db_type or not connection_details:
            raise HTTPException(status_code=400, detail="db_type and connection_details are required")

        # For now, return mock datasets since we don't have actual database connections
        # In a real implementation, this would connect to the database and discover tables
        mock_datasets = [
            {
                "name": "customers",
                "description": "Customer information and demographics",
                "rows": 10000,
                "columns": ["customer_id", "name", "age", "gender", "city", "signup_date", "total_purchases"],
                "table_type": "table"
            },
            {
                "name": "orders",
                "description": "Order transactions and details",
                "rows": 50000,
                "columns": ["order_id", "customer_id", "product_id", "quantity", "price", "order_date", "status"],
                "table_type": "table"
            },
            {
                "name": "products",
                "description": "Product catalog and information",
                "rows": 1000,
                "columns": ["product_id", "name", "category", "price", "stock", "rating", "description"],
                "table_type": "table"
            },
            {
                "name": "user_activity",
                "description": "User website activity and behavior",
                "rows": 75000,
                "columns": ["user_id", "session_id", "page_views", "time_spent", "clicks", "bounce_rate"],
                "table_type": "table"
            }
        ]

        return {
            "success": True,
            "datasets": mock_datasets,
            "connection_info": {
                "db_type": db_type,
                "host": connection_details.get("host"),
                "database": connection_details.get("database")
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in discover_datasets: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/suggest_datasets")
async def suggest_datasets_endpoint(request: Dict[str, Any]):
    """Suggest datasets based on user query"""
    try:
        user_query = request.get("user_query", "")
        available_datasets = request.get("available_datasets", [])

        if not user_query or not available_datasets:
            raise HTTPException(status_code=400, detail="user_query and available_datasets are required")

        # Simple keyword-based recommendation logic
        query_lower = user_query.lower()
        scored_datasets = []

        for dataset in available_datasets:
            score = 0
            name_lower = dataset["name"].lower()
            desc_lower = dataset.get("description", "").lower()
            columns = dataset.get("columns", [])

            # Keyword matching
            if "customer" in query_lower and "customer" in (name_lower + desc_lower):
                score += 3
            if "sales" in query_lower and "sales" in (name_lower + desc_lower):
                score += 3
            if "user" in query_lower and "user" in (name_lower + desc_lower):
                score += 3
            if "order" in query_lower and "order" in (name_lower + desc_lower):
                score += 3
            if "product" in query_lower and "product" in (name_lower + desc_lower):
                score += 2
            if "activity" in query_lower and "activity" in (name_lower + desc_lower):
                score += 2

            # Column relevance
            for col in columns:
                col_lower = col.lower()
                if any(word in col_lower for word in query_lower.split()):
                    score += 1

            # Data size bonus (prefer medium-sized datasets)
            rows = dataset.get("rows", 0)
            if 1000 <= rows <= 100000:
                score += 1

            scored_datasets.append({
                "dataset_name": dataset["name"],
                "confidence": min(score / 10.0, 1.0),  # Normalize to 0-1
                "reasoning": f"Dataset '{dataset['name']}' matches your query with score {score}/10",
                "dataset_info": dataset
            })

        # Sort by confidence
        scored_datasets.sort(key=lambda x: x["confidence"], reverse=True)

        return {
            "recommended_datasets": scored_datasets[:3],  # Top 3 recommendations
            "total_datasets": len(available_datasets)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in suggest_datasets: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AI Data Science Pipeline Orchestrator",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Data Science Pipeline Orchestrator",
        "version": "1.0.0",
        "docs": "/docs"
    }