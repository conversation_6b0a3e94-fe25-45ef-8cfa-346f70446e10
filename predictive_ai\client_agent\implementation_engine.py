"""
IMPLEMENTATION ENGINE
Executes alternatives and user requests with real data/model modifications
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from loguru import logger
from pathlib import Path
from datetime import datetime
import re

# Try to import ML libraries
try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
    from sklearn.impute import SimpleImputer
    from sklearn.feature_selection import SelectKBest, RFE
    from sklearn.preprocessing import PolynomialFeatures
    from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("Scikit-learn not available, using mock implementations")


class ImplementationEngine:
    """Executes real implementations based on alternatives or user requests"""
    
    def __init__(self):
        self.implementation_handlers = {
            'data_cleaning': self._execute_data_cleaning,
            'feature_engineering': self._execute_feature_engineering,
            'model_training': self._execute_model_training,
            'hyperparameter_tuning': self._execute_hyperparameter_tuning,
            'model_evaluation': self._execute_model_evaluation,
            'generic': self._execute_generic
        }
    
    def execute_alternative(self, alternative: Dict[str, Any], step_data: Dict[str, Any], 
                          pipeline_id: str) -> Dict[str, Any]:
        """Execute a specific alternative"""
        
        try:
            implementation = alternative.get('implementation', {})
            impl_type = implementation.get('type', 'generic')
            
            if impl_type in self.implementation_handlers:
                result = self.implementation_handlers[impl_type](
                    implementation, alternative, step_data, pipeline_id
                )
                
                # Add metadata
                result.update({
                    'alternative_id': alternative.get('id'),
                    'alternative_title': alternative.get('title'),
                    'execution_timestamp': datetime.now().isoformat()
                })
                
                return result
            else:
                return {
                    'success': False,
                    'error': f"Unknown implementation type: {impl_type}"
                }
                
        except Exception as e:
            logger.error(f"Alternative execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_user_request(self, user_message: str, step_data: Dict[str, Any], 
                           pipeline_id: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute user request from natural language"""
        
        try:
            # Parse user intent and extract entities
            intent_data = self._parse_user_intent(user_message, context or {})
            
            if intent_data['success']:
                # Convert to implementation format
                implementation = self._convert_intent_to_implementation(intent_data)
                
                # Execute the implementation
                result = self._execute_implementation(implementation, step_data, pipeline_id)
                
                result.update({
                    'user_request': user_message,
                    'parsed_intent': intent_data,
                    'execution_timestamp': datetime.now().isoformat()
                })
                
                return result
            else:
                return {
                    'success': False,
                    'error': f"Could not understand request: {intent_data.get('error', 'Unknown error')}"
                }
                
        except Exception as e:
            logger.error(f"User request execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _parse_user_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse user intent using NLP and pattern matching"""
        
        message_lower = message.lower().strip()
        
        # Feature engineering patterns
        if re.search(r'create.*column.*group.*by.*(\w+)', message_lower):
            match = re.search(r'group.*by.*(\w+)', message_lower)
            group_column = match.group(1) if match else 'unknown'
            
            # Look for aggregation function
            agg_match = re.search(r'(average|mean|avg|sum|count|max|min)', message_lower)
            agg_function = agg_match.group(1) if agg_match else 'mean'
            
            # Look for target column
            target_match = re.search(r'(average|mean|avg|sum|count|max|min).*(\w+)', message_lower)
            target_column = target_match.group(2) if target_match else 'price'
            
            return {
                'success': True,
                'intent_type': 'feature_engineering',
                'operation': 'group_aggregate',
                'entities': {
                    'group_column': group_column,
                    'target_column': target_column,
                    'aggregation': agg_function,
                    'new_column_name': f'{group_column}_{agg_function}_{target_column}'
                }
            }
        
        # Scaling patterns
        elif re.search(r'(standard|normalize|minmax|robust).*scal', message_lower):
            scaler_match = re.search(r'(standard|normalize|minmax|robust)', message_lower)
            scaler_type = scaler_match.group(1) if scaler_match else 'standard'
            
            return {
                'success': True,
                'intent_type': 'data_cleaning',
                'operation': 'scale_features',
                'entities': {
                    'scaler_type': scaler_type
                }
            }
        
        # Missing value patterns
        elif re.search(r'(fill|impute).*missing', message_lower) or re.search(r'missing.*value', message_lower):
            method_match = re.search(r'(mean|median|mode|forward|backward)', message_lower)
            method = method_match.group(1) if method_match else 'mean'
            
            return {
                'success': True,
                'intent_type': 'data_cleaning',
                'operation': 'handle_missing',
                'entities': {
                    'method': method
                }
            }
        
        # Outlier patterns
        elif re.search(r'(remove|handle).*outlier', message_lower):
            method_match = re.search(r'(iqr|zscore|isolation)', message_lower)
            method = method_match.group(1) if method_match else 'iqr'
            
            return {
                'success': True,
                'intent_type': 'data_cleaning',
                'operation': 'handle_outliers',
                'entities': {
                    'method': method
                }
            }
        
        # Model training patterns
        elif re.search(r'(train|use).*model', message_lower) or re.search(r'algorithm', message_lower):
            model_match = re.search(r'(random.?forest|xgboost|svm|linear|logistic)', message_lower)
            model_type = model_match.group(1) if model_match else 'random_forest'
            
            return {
                'success': True,
                'intent_type': 'model_training',
                'operation': 'train_model',
                'entities': {
                    'algorithm': model_type.replace(' ', '_')
                }
            }
        
        # Parameter change patterns
        elif re.search(r'set\s+(\w+)\s+to\s+([0-9.]+)', message_lower):
            param_match = re.search(r'set\s+(\w+)\s+to\s+([0-9.]+)', message_lower)
            if param_match:
                param_name = param_match.group(1)
                param_value = float(param_match.group(2))
                
                return {
                    'success': True,
                    'intent_type': 'parameter_change',
                    'operation': 'update_parameter',
                    'entities': {
                        'parameter': param_name,
                        'value': param_value
                    }
                }
        
        # Generic feature creation
        elif re.search(r'(create|add).*column', message_lower):
            return {
                'success': True,
                'intent_type': 'feature_engineering',
                'operation': 'create_feature',
                'entities': {
                    'feature_type': 'custom'
                }
            }
        
        return {
            'success': False,
            'error': f"Could not parse intent from: {message}"
        }
    
    def _convert_intent_to_implementation(self, intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert parsed intent to implementation format"""
        
        intent_type = intent_data['intent_type']
        operation = intent_data['operation']
        entities = intent_data['entities']
        
        if intent_type == 'feature_engineering' and operation == 'group_aggregate':
            return {
                'type': 'feature_engineering',
                'operation': 'group_aggregate',
                'config': {
                    'group_column': entities['group_column'],
                    'target_column': entities['target_column'],
                    'aggregation': entities['aggregation'],
                    'new_column_name': entities['new_column_name']
                }
            }
        
        elif intent_type == 'data_cleaning' and operation == 'scale_features':
            scaler_map = {
                'standard': 'StandardScaler',
                'normalize': 'StandardScaler',
                'minmax': 'MinMaxScaler',
                'robust': 'RobustScaler'
            }
            
            return {
                'type': 'data_cleaning',
                'operation': 'scale_features',
                'config': {
                    'scaler': scaler_map.get(entities['scaler_type'], 'StandardScaler')
                }
            }
        
        elif intent_type == 'data_cleaning' and operation == 'handle_missing':
            return {
                'type': 'data_cleaning',
                'operation': 'impute_missing',
                'config': {
                    'strategy': entities['method']
                }
            }
        
        elif intent_type == 'data_cleaning' and operation == 'handle_outliers':
            return {
                'type': 'data_cleaning',
                'operation': 'remove_outliers',
                'config': {
                    'method': entities['method']
                }
            }
        
        elif intent_type == 'model_training':
            algorithm_map = {
                'random_forest': 'RandomForestClassifier',
                'xgboost': 'XGBClassifier',
                'svm': 'SVC',
                'linear': 'LinearRegression',
                'logistic': 'LogisticRegression'
            }
            
            return {
                'type': 'model_training',
                'operation': 'train_model',
                'config': {
                    'algorithm': algorithm_map.get(entities['algorithm'], 'RandomForestClassifier')
                }
            }
        
        elif intent_type == 'parameter_change':
            return {
                'type': 'parameter_change',
                'operation': 'update_parameter',
                'config': {
                    'parameter': entities['parameter'],
                    'value': entities['value']
                }
            }
        
        else:
            return {
                'type': 'generic',
                'operation': 'custom',
                'config': entities
            }
    
    def _execute_implementation(self, implementation: Dict[str, Any], 
                              step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute implementation based on type"""
        
        impl_type = implementation.get('type', 'generic')
        
        if impl_type in self.implementation_handlers:
            return self.implementation_handlers[impl_type](
                implementation, {}, step_data, pipeline_id
            )
        else:
            return {
                'success': False,
                'error': f"Unknown implementation type: {impl_type}"
            }

    def _execute_data_cleaning(self, implementation: Dict[str, Any], alternative: Dict[str, Any],
                             step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute data cleaning operations"""

        try:
            operation = implementation.get('operation')
            config = implementation.get('config', {})

            # Get current dataset
            dataset_path = self._get_current_dataset_path(step_data)
            if not dataset_path:
                return {'success': False, 'error': 'No dataset available'}

            df = pd.read_csv(dataset_path)
            original_shape = df.shape

            if operation == 'impute_missing':
                strategy = config.get('strategy', 'mean')
                numeric_columns = df.select_dtypes(include=[np.number]).columns

                if strategy == 'mean':
                    df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())
                elif strategy == 'median':
                    df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].median())
                elif strategy == 'forward' or strategy == 'ffill':
                    df = df.fillna(method='ffill')

                description = f"Filled missing values using {strategy} strategy"

            elif operation == 'remove_outliers':
                method = config.get('method', 'iqr')
                numeric_columns = df.select_dtypes(include=[np.number]).columns

                if method == 'iqr':
                    for col in numeric_columns:
                        Q1 = df[col].quantile(0.25)
                        Q3 = df[col].quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]

                elif method == 'zscore':
                    from scipy import stats
                    z_scores = np.abs(stats.zscore(df[numeric_columns]))
                    df = df[(z_scores < 3).all(axis=1)]

                description = f"Removed outliers using {method} method"

            elif operation == 'scale_features':
                scaler_type = config.get('scaler', 'StandardScaler')
                numeric_columns = df.select_dtypes(include=[np.number]).columns

                if SKLEARN_AVAILABLE:
                    if scaler_type == 'StandardScaler':
                        scaler = StandardScaler()
                    elif scaler_type == 'MinMaxScaler':
                        scaler = MinMaxScaler()
                    elif scaler_type == 'RobustScaler':
                        scaler = RobustScaler()
                    else:
                        scaler = StandardScaler()

                    df[numeric_columns] = scaler.fit_transform(df[numeric_columns])
                else:
                    # Mock scaling
                    df[numeric_columns] = (df[numeric_columns] - df[numeric_columns].mean()) / df[numeric_columns].std()

                description = f"Applied {scaler_type} to numeric features"

            elif operation == 'drop_missing':
                df = df.dropna()
                description = "Dropped rows with missing values"

            else:
                return {'success': False, 'error': f"Unknown operation: {operation}"}

            # Save modified dataset
            new_path = self._save_modified_dataset(df, dataset_path, f"cleaned_{operation}")

            # Update step data
            step_data['modified_dataset_path'] = new_path
            step_data['data_modifications'] = step_data.get('data_modifications', [])
            step_data['data_modifications'].append({
                'operation': operation,
                'description': description,
                'original_shape': original_shape,
                'new_shape': df.shape,
                'config': config
            })

            return {
                'success': True,
                'description': description,
                'result_summary': f"Dataset shape: {original_shape} → {df.shape}",
                'new_dataset_path': new_path,
                'operation': operation
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _execute_feature_engineering(self, implementation: Dict[str, Any], alternative: Dict[str, Any],
                                   step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute feature engineering operations"""

        try:
            operation = implementation.get('operation')
            config = implementation.get('config', {})

            dataset_path = self._get_current_dataset_path(step_data)
            if not dataset_path:
                return {'success': False, 'error': 'No dataset available'}

            df = pd.read_csv(dataset_path)
            original_shape = df.shape

            if operation == 'group_aggregate':
                group_column = config.get('group_column', 'zipcode')
                target_column = config.get('target_column', 'price')
                aggregation = config.get('aggregation', 'mean')
                new_column_name = config.get('new_column_name', f'{group_column}_{aggregation}_{target_column}')

                if group_column in df.columns and target_column in df.columns:
                    if aggregation in ['mean', 'average', 'avg']:
                        grouped_data = df.groupby(group_column)[target_column].mean()
                    elif aggregation == 'sum':
                        grouped_data = df.groupby(group_column)[target_column].sum()
                    elif aggregation == 'count':
                        grouped_data = df.groupby(group_column)[target_column].count()
                    elif aggregation == 'max':
                        grouped_data = df.groupby(group_column)[target_column].max()
                    elif aggregation == 'min':
                        grouped_data = df.groupby(group_column)[target_column].min()
                    else:
                        grouped_data = df.groupby(group_column)[target_column].mean()

                    df[new_column_name] = df[group_column].map(grouped_data)
                    description = f"Created column '{new_column_name}' by grouping '{group_column}' and calculating {aggregation} of '{target_column}'"
                else:
                    return {'success': False, 'error': f"Columns '{group_column}' or '{target_column}' not found"}

            elif operation == 'polynomial_features':
                degree = config.get('degree', 2)
                numeric_columns = df.select_dtypes(include=[np.number]).columns[:3]  # Limit to first 3 numeric columns

                if SKLEARN_AVAILABLE:
                    poly = PolynomialFeatures(degree=degree, include_bias=False)
                    poly_features = poly.fit_transform(df[numeric_columns])
                    feature_names = poly.get_feature_names_out(numeric_columns)

                    # Add polynomial features to dataframe
                    for i, name in enumerate(feature_names[len(numeric_columns):]):  # Skip original features
                        df[f'poly_{name}'] = poly_features[:, len(numeric_columns) + i]
                else:
                    # Mock polynomial features
                    for i, col1 in enumerate(numeric_columns):
                        for j, col2 in enumerate(numeric_columns[i:], i):
                            if i != j:
                                df[f'poly_{col1}_{col2}'] = df[col1] * df[col2]

                description = f"Created polynomial features of degree {degree}"

            elif operation == 'log_transform':
                numeric_columns = df.select_dtypes(include=[np.number]).columns
                add_constant = config.get('add_constant', 1)

                for col in numeric_columns:
                    if (df[col] > 0).all():  # Only apply to positive values
                        df[f'log_{col}'] = np.log(df[col] + add_constant)

                description = "Applied log transformation to positive numeric features"

            else:
                return {'success': False, 'error': f"Unknown operation: {operation}"}

            # Save modified dataset
            new_path = self._save_modified_dataset(df, dataset_path, f"featured_{operation}")

            # Update step data
            step_data['modified_dataset_path'] = new_path
            step_data['feature_engineering'] = step_data.get('feature_engineering', [])
            step_data['feature_engineering'].append({
                'operation': operation,
                'description': description,
                'original_shape': original_shape,
                'new_shape': df.shape,
                'config': config
            })

            return {
                'success': True,
                'description': description,
                'result_summary': f"Dataset shape: {original_shape} → {df.shape}",
                'new_dataset_path': new_path,
                'operation': operation
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _execute_model_training(self, implementation: Dict[str, Any], alternative: Dict[str, Any],
                              step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute model training operations"""

        try:
            operation = implementation.get('operation')
            config = implementation.get('config', {})

            if operation == 'train_model':
                algorithm = config.get('algorithm', 'RandomForestClassifier')
                params = config.get('params', {})

                # Store model training request
                step_data['model_training_request'] = {
                    'algorithm': algorithm,
                    'parameters': params,
                    'timestamp': datetime.now().isoformat()
                }

                description = f"Initiated training with {algorithm}"
                return {
                    'success': True,
                    'description': description,
                    'result_summary': f"Model training queued: {algorithm}",
                    'operation': operation,
                    'algorithm': algorithm
                }

            return {'success': False, 'error': f"Unknown operation: {operation}"}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _execute_hyperparameter_tuning(self, implementation: Dict[str, Any], alternative: Dict[str, Any],
                                     step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute hyperparameter tuning operations"""

        try:
            operation = implementation.get('operation')
            config = implementation.get('config', {})

            # Store hyperparameter tuning request
            step_data['hyperparameter_tuning_request'] = {
                'method': operation,
                'config': config,
                'timestamp': datetime.now().isoformat()
            }

            description = f"Initiated {operation} hyperparameter tuning"
            return {
                'success': True,
                'description': description,
                'result_summary': f"Hyperparameter tuning queued: {operation}",
                'operation': operation
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _execute_model_evaluation(self, implementation: Dict[str, Any], alternative: Dict[str, Any],
                                step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute model evaluation operations"""

        try:
            operation = implementation.get('operation')
            config = implementation.get('config', {})

            # Store evaluation request
            step_data['evaluation_request'] = {
                'method': operation,
                'config': config,
                'timestamp': datetime.now().isoformat()
            }

            description = f"Initiated {operation} evaluation"
            return {
                'success': True,
                'description': description,
                'result_summary': f"Model evaluation queued: {operation}",
                'operation': operation
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _execute_generic(self, implementation: Dict[str, Any], alternative: Dict[str, Any],
                       step_data: Dict[str, Any], pipeline_id: str) -> Dict[str, Any]:
        """Execute generic operations"""

        try:
            operation = implementation.get('operation', 'custom')
            config = implementation.get('config', {})

            description = f"Executed {operation} operation"
            return {
                'success': True,
                'description': description,
                'result_summary': f"Operation completed: {operation}",
                'operation': operation
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_current_dataset_path(self, step_data: Dict[str, Any]) -> Optional[str]:
        """Get the current dataset path"""
        # Check for modified dataset first
        if 'modified_dataset_path' in step_data:
            return step_data['modified_dataset_path']
        # Check for cleaned dataset
        if 'cleaned_data_path' in step_data:
            return step_data['cleaned_data_path']
        # Check for original dataset
        if 'dataset_path' in step_data:
            return step_data['dataset_path']
        return None

    def _save_modified_dataset(self, df: pd.DataFrame, original_path: str, suffix: str) -> str:
        """Save modified dataset with timestamp"""
        try:
            path_obj = Path(original_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_name = f"{path_obj.stem}_{suffix}_{timestamp}{path_obj.suffix}"
            new_path = path_obj.parent / new_name

            df.to_csv(new_path, index=False)
            logger.info(f"Saved modified dataset: {new_path}")
            return str(new_path)
        except Exception as e:
            logger.error(f"Failed to save dataset: {e}")
            return original_path
