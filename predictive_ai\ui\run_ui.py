#!/usr/bin/env python3
"""
Simple launcher script for the AI Data Science Pipeline Streamlit UI
"""
import subprocess
import sys
import os

def main():
    """Launch the Streamlit UI"""
    # Get the directory of this script
    ui_dir = os.path.dirname(os.path.abspath(__file__))
    app_path = os.path.join(ui_dir, "app.py")
    
    # Check if app.py exists
    if not os.path.exists(app_path):
        print("❌ Error: app.py not found in the UI directory")
        sys.exit(1)
    
    print("🚀 Starting AI Data Science Pipeline UI...")
    print("📍 UI will be available at: http://localhost:8501")
    print("🔄 Starting Streamlit server...")
    
    try:
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", app_path,
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--browser.gatherUsageStats", "false"
        ], check=True)
    except KeyboardInterrupt:
        print("\n⏹️  UI server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting UI: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
